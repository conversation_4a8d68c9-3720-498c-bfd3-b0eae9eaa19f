from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Union
import pandas as pd
import requests
import time
import logging
import aiohttp # Added for async requests
import asyncio
logger = logging.getLogger(__name__)

@dataclass
class Balance:
    symbol: str
    total: float
    available: float
    in_order: float
    
@dataclass
class Position:
    symbol: str
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    
class ExchangeInterface(ABC):
    """Base interface that all exchange implementations must follow."""
    
    def __init__(self):
        # This will hold all standardized instrument information
        self.instrument_standardized_info: List[Dict] = []
        self.collateral_pool_name = "not_defined"
    
    @abstractmethod
    async def initialize_standardized_instrument_info(self):
        """
        Asynchronously initializes the standardized instrument information.
        This method should be called once after the exchange object is created.
        """
        pass
        
    @abstractmethod
    async def update_standardized_instrument_info(self):
        """
        Asynchronously updates the standardized instrument information. 
        This might be called periodically.
        """
        pass
    
    @abstractmethod
    async def get_standardized_instrument_info(self) -> List[Dict]:
        """Fetch and standardize instrument information from the exchange."""
        pass
    
    @abstractmethod
    def convert_to_spot_standardized_symbol(self, exchange_symbol: str) -> str:
        """Convert exchange-specific spot symbol to standardized format."""
        pass
    
    @abstractmethod
    def convert_to_perp_standardized_symbol(self, exchange_symbol: str, skip_exchange_specific_stable_conversion: bool = False) -> str:
        """Convert exchange-specific perpetual symbol to standardized format."""
        pass
    
    @abstractmethod
    def convert_to_spot_exchange_symbol(self, standardized_symbol: str) -> str:
        """Convert standardized spot symbol to exchange-specific format."""
        pass
    
    @abstractmethod
    def convert_to_perp_exchange_symbol(self, standardized_symbol: str, skip_exchange_specific_stable_conversion: bool = False) -> str:
        """Convert standardized perpetual symbol to exchange-specific format."""
        pass

    def supports_funding_rates(self) -> bool:
        """Return True if this exchange supports funding rates (has perpetual futures)"""
        # Default implementation - subclasses should override if they support funding
        return False
    
    @abstractmethod
    def get_balances(self, nonzero_only: bool = True) -> List[Balance]:
        """Get all balances for the account."""
        pass

    @abstractmethod
    def get_positions(self, nonzero_only: bool = True) -> List[Position]:
        """Get all positions for the account."""
        pass

    @abstractmethod
    def get_standardized_unified_data(self, nonzero_only: bool = True) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Get standardized unified data containing both balances and positions."""
        pass

    @abstractmethod
    def get_mark_prices(self, standardized_symbols: List[str]) -> Dict[str, float]:
        """Get mark prices for given standardized symbols."""
        pass

    # @abstractmethod
    # def get_collateral_info(self) -> Dict[str, any]:
    #     """Get collateral info from the exchange."""
    #     pass
    
    @abstractmethod
    async def _get_current_price(self, exchange_symbol: str) -> float:
        """
        Get current price for a standardized symbol.
        """
        pass
    
    @abstractmethod
    def get_eligible_collateral_qty(self, standardized_symbol: str, usd_value: float) -> float:
        """Calculate the eligible collateral quantity for a given symbol and USD value."""
        pass
    
    @abstractmethod
    def get_tickers(self, category: str) -> Dict:
        """Get tickers for a category."""
        pass
        
    async def _get_price_with_subscription(self, exchange_name: str, symbol: str, session: aiohttp.ClientSession) -> float:
        """
        Get current price for a symbol asynchronously, handling subscription and health checks.
        """
        price = 0.0
        symbol_for_service = symbol  # Use the direct instrument name provided
        # Convert exchange name for data service if necessary
        data_service_exchange_name = exchange_name
        if data_service_exchange_name == "crypto_com" or data_service_exchange_name == "crypto_com_futures":
            data_service_exchange_name = "crypto-com"

        url = f"http://localhost:8003/price/{data_service_exchange_name}/{symbol_for_service}"

        try:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                #print("response latency = ", response.elapsed.total_seconds())
                if response.status == 200:
                    data = await response.json()
                    bid = float(data.get('bid', 0))
                    ask = float(data.get('ask', 0))
                    if bid > 0 and ask > 0:
                        price = (bid + ask) / 2
                    else:
                        logger.warning(
                            f"Received invalid bid/ask from price service for {data_service_exchange_name} {symbol_for_service}: bid={bid}, ask={ask}")
                elif response.status == 404:
                    logger.warning(
                        f"Price data not found (404) for {data_service_exchange_name} {symbol_for_service}. Attempting to subscribe and retrying...")

                    # Attempt to subscribe to the symbol
                    await self._ensure_subscribed(data_service_exchange_name, symbol_for_service, session)

                    # Retry price fetch (with health check if needed)
                    retry_count = 5
                    for attempt in range(retry_count):  # Retry for up to 10 seconds (5 attempts * 2 seconds)
                        await asyncio.sleep(2)
                        try:
                            async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as retry_response:
                                if retry_response.status == 200:
                                    retry_data = await retry_response.json()
                                    bid = float(retry_data.get('bid', 0))
                                    ask = float(retry_data.get('ask', 0))
                                    if bid > 0 and ask > 0:
                                        price = (bid + ask) / 2
                                        logger.info(
                                            f"Successfully fetched price for {data_service_exchange_name} {symbol_for_service} after {attempt + 1} retries.")
                                        break  # Price found, exit retry loop
                                    else:
                                        logger.warning(
                                            f"Attempt {attempt + 1}: Invalid bid/ask from price service for {data_service_exchange_name} {symbol_for_service}: bid={bid}, ask={ask}")
                                else:
                                    logger.warning(
                                        f"Attempt {attempt + 1}: Price still not available for {data_service_exchange_name} {symbol_for_service} (status {retry_response.status}).")
                        except aiohttp.ClientConnectorError as retry_e:
                            logger.warning(
                                f"Attempt {attempt + 1}: ClientConnectorError fetching price for {data_service_exchange_name} {symbol_for_service}: {retry_e}")
                        except asyncio.TimeoutError:
                            logger.warning(f"Attempt {attempt + 1}: Timeout fetching price for {data_service_exchange_name} {symbol_for_service}.")
                        except Exception as retry_e:
                            logger.error(
                                f"Attempt {attempt + 1}: Unexpected error fetching price for {data_service_exchange_name} {symbol_for_service}: {retry_e}",
                                exc_info=True)
                    else:
                        logger.warning(f"Price still unavailable for {data_service_exchange_name} {symbol_for_service} after {retry_count} retries.")

                    if price <= 0:
                        logger.error(
                            f"Failed to fetch price for {data_service_exchange_name} {symbol_for_service} after multiple retries. Checking data service health...")
                        # 4. Monitor health endpoint
                        healthy = False
                        health_check_url = "http://localhost:8003/health"
                        health_check_attempts = 10
                        for health_attempt in range(health_check_attempts):  # Check health for up to 20 seconds
                            try:
                                async with session.get(health_check_url, timeout=aiohttp.ClientTimeout(total=5)) as health_response:
                                    if health_response.status == 200 and (await health_response.json()).get("status") == "ok":
                                        healthy = True
                                        logger.info("Data service is healthy again. Retrying price fetch...")
                                        break
                                    else:
                                        logger.warning(
                                            f"Data service health check failed (attempt {health_attempt + 1}): {health_response.status} - {await health_response.text()}")
                            except aiohttp.ClientConnectorError as health_e:
                                logger.warning(
                                    f"Data service health check request failed (attempt {health_attempt + 1}): {health_e}")
                            except asyncio.TimeoutError:
                                logger.warning(f"Attempt {health_attempt + 1}: Timeout during data service health check.")
                            except Exception as health_e:
                                logger.error(
                                    f"Error during data service health check (attempt {health_attempt + 1}): {health_e}",
                                    exc_info=True)
                            await asyncio.sleep(2)

                        if healthy:
                            try:
                                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as retry_response:
                                    if retry_response.status == 200:
                                        retry_data = await retry_response.json()
                                        bid = float(retry_data.get('bid', 0))
                                        ask = float(retry_data.get('ask', 0))
                                        if bid > 0 and ask > 0:
                                            price = (bid + ask) / 2
                                            logger.info(
                                                f"Successfully fetched price for {data_service_exchange_name} {symbol_for_service} after health check and retry.")
                                        else:
                                            logger.warning(
                                                f"Invalid bid/ask from price service for {data_service_exchange_name} {symbol_for_service} after health check: bid={bid}, ask={ask}")
                                    else:
                                        logger.warning(
                                            f"Price still not available for {data_service_exchange_name} {symbol_for_service} after health check (status {retry_response.status}).")
                            except aiohttp.ClientConnectorError as retry_e:
                                logger.warning(
                                    f"ClientConnectorError fetching price after health check for {data_service_exchange_name} {symbol_for_service}: {retry_e}")
                            except asyncio.TimeoutError:
                                logger.warning(f"Timeout fetching price after health check for {data_service_exchange_name} {symbol_for_service}.")
                            except Exception as retry_e:
                                logger.error(
                                    f"Error fetching price after health check for {data_service_exchange_name} {symbol_for_service}: {retry_e}",
                                    exc_info=True)
                            if price <= 0:
                                logger.error(
                                    f"Price still unavailable for {data_service_exchange_name} {symbol_for_service} after health check and retry. Giving up.")
                        else:
                            logger.error(
                                f"Data service is unhealthy after multiple checks. Price fetch failed for {data_service_exchange_name} {symbol_for_service}.")
                else:
                    logger.error(
                        f"Error fetching price from data service for {data_service_exchange_name} {symbol_for_service}: {response.status} - {await response.text()}")

        except (aiohttp.ClientConnectorError, asyncio.TimeoutError) as e:
            # Catch connection errors and timeouts for retry
            logger.warning(f"Connection error or timeout fetching price for {data_service_exchange_name} {symbol_for_service}: {e}. Retrying...")
            # The retry logic is already implemented above for 404s.
            # We need to ensure these exceptions also trigger that retry loop.
            # The current structure with the `else` block after the first `async with session.get`
            # handles this by falling through to the retry logic if the initial fetch fails
            # with an exception. No explicit change needed here, just confirmation that
            # these exceptions are caught and don't immediately return 0.
            pass # The retry logic is handled by the outer structure
        except Exception as e:
            logger.error(f"Unexpected error fetching price for {data_service_exchange_name} {symbol_for_service}: {e}", exc_info=True)
            # For unexpected errors not related to connection, we might not want to retry indefinitely.
            # The current logic will fall through and potentially trigger the health check and final retry.
            # If price is still 0 after that, it will be logged as a failure.

        return price

    async def _ensure_subscribed(self, exchange_name: str, symbol: str, session: aiohttp.ClientSession):
        """
        Checks if a symbol is subscribed to the data service and attempts to add the subscription if not.
        Uses the provided aiohttp session.
        """
        # Convert exchange name for data service if necessary
        data_service_exchange_name = exchange_name
        if data_service_exchange_name == "crypto_com" or data_service_exchange_name == "crypto_com_futures":
            data_service_exchange_name = "crypto-com"

        try:
            options_url = "http://localhost:8003/get_stream_options"
            async with session.get(options_url, timeout=aiohttp.ClientTimeout(total=5)) as options_response:
                if options_response.status == 200:
                    current_options_data = await options_response.json()
                    current_stream_options = current_options_data.get("stream_options", [])

                    is_subscribed = False
                    for option in current_stream_options:
                        if option.get("exchange") == data_service_exchange_name and symbol in option.get("symbols", []):
                            is_subscribed = True
                            break

                    if not is_subscribed:
                        logger.info(
                            f"Symbol {data_service_exchange_name} {symbol} is NOT SUBSCRIBED. Attempting to add...")
                        add_subscription_url = "http://localhost:8003/add_subscription"
                        subscription_data = {
                            "exchange": data_service_exchange_name,
                            "symbol": symbol,
                            "dataTypes": ["derivative_ticker", "book_snapshot_1_500ms"]
                        }
                        async with session.post(add_subscription_url, json=subscription_data, timeout=aiohttp.ClientTimeout(total=10)) as add_response:

                            if add_response.status == 200:
                                logger.info(
                                    f"Successfully requested subscription for {data_service_exchange_name} {symbol}.")
                            else:
                                logger.error(
                                    f"Failed to add subscription for {data_service_exchange_name} {symbol} (status {add_response.status}): {await add_response.text()}")
                else:
                    logger.error(
                        f"Failed to get stream options (status {options_response.status}) while checking subscription for {symbol}. Cannot auto-subscribe.")

        except aiohttp.ClientConnectorError as e:
            logger.error(f"ClientConnectorError during subscription check/attempt for {data_service_exchange_name} {symbol}: {e}")
        except asyncio.TimeoutError:
             logger.error(f"Timeout during subscription check/attempt for {data_service_exchange_name} {symbol}.")
        except Exception as e:
            logger.error(
                f"Unexpected error during subscription check/attempt for {data_service_exchange_name} {symbol}: {e}",
                exc_info=True)

    async def _get_funding_data(self, exchange_name: str, symbol: str, session: aiohttp.ClientSession) -> dict:
        """Fetch funding data asynchronously from the tardis API service running on localhost:8003"""
        # Convert exchange name for data service if necessary
        data_service_exchange_name = exchange_name
        if data_service_exchange_name == "crypto_com" or data_service_exchange_name == "crypto_com_futures":
            data_service_exchange_name = "crypto-com"

        url = f"http://localhost:8003/funding/{data_service_exchange_name}/{symbol}"

        try:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                elif response.status == 404:
                    logger.warning(
                        f"Funding data not found (404) for {data_service_exchange_name} {symbol}. Attempting to subscribe and retrying...")

                    # Attempt to subscribe to the symbol
                    await self._ensure_subscribed(data_service_exchange_name, symbol, session)

                    # Retry funding data fetch
                    retry_count = 5
                    for attempt in range(retry_count):
                        await asyncio.sleep(2)
                        try:
                            async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as retry_response:
                                if retry_response.status == 200:
                                    logger.info(f"Successfully fetched funding data for {data_service_exchange_name} {symbol} after {attempt + 1} retries.")
                                    return await retry_response.json()
                                else:
                                    logger.warning(
                                        f"Attempt {attempt + 1}: Funding data still not available for {data_service_exchange_name} {symbol} (status {retry_response.status}).")
                        except aiohttp.ClientConnectorError as retry_e:
                            logger.warning(
                                f"Attempt {attempt + 1}: ClientConnectorError fetching funding data for {data_service_exchange_name} {symbol}: {retry_e}")
                        except asyncio.TimeoutError:
                            logger.warning(f"Attempt {attempt + 1}: Timeout fetching funding data for {data_service_exchange_name} {symbol}.")
                        except Exception as retry_e:
                            logger.error(
                                f"Attempt {attempt + 1}: Unexpected error fetching funding data for {data_service_exchange_name} {symbol}: {retry_e}",
                                exc_info=True)
                else:
                    logger.error(f"API call failed with status {response.status} for funding data for {data_service_exchange_name} {symbol}")
                    return {}

        except aiohttp.ClientConnectorError as e:
            logger.error(f"ClientConnectorError fetching funding data for {data_service_exchange_name} {symbol}: {e}")
            return {}
        except asyncio.TimeoutError:
             logger.error(f"Timeout fetching funding data for {data_service_exchange_name} {symbol}.")
             return {}
        except Exception as e:
            logger.error(f"Unexpected error fetching funding data for {data_service_exchange_name} {symbol}: {e}", exc_info=True)
            return {}

    async def _get_spread_data(self, spot_market: Dict[str, str], perp_markets: List[Dict[str, str]], session: aiohttp.ClientSession) -> Optional[Dict[str, float]]:
        """
        Fetch spot-perp spread data asynchronously from the data service running on localhost:8003.
        Returns a dictionary with 'avg_bid_bid_bp' and 'avg_ask_ask_bp' or None if data is unavailable/error.
        """
        url = "http://localhost:8003/calculate_spread"
        payload = {
            "spot_market": spot_market,
            "perp_markets": perp_markets
        }
        
        try:
            async with session.post(url, json=payload, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    spread_data = await response.json()
                    # Ensure the expected keys are present
                    if 'avg_bid_bid_bp' in spread_data and 'avg_ask_ask_bp' in spread_data:
                        return {
                            'avg_bid_bid_bp': spread_data['avg_bid_bid_bp'],
                            'avg_ask_ask_bp': spread_data['avg_ask_ask_bp']
                        }
                    else:
                        logger.warning(f"Received unexpected data format from /calculate_spread: {spread_data}")
                        return None
                elif response.status == 404:
                    # This is expected if data isn't available yet, not an error
                    logger.debug(f"Spread data not found (404) for Spot: {spot_market}, Perps: {perp_markets}")
                    return None
                else:
                    logger.error(f"Error fetching spread from data service. Status: {response.status}, Response: {await response.text()}, Payload: {payload}")
                    return None
        except aiohttp.ClientConnectorError as e:
            logger.error(f"ClientConnectorError fetching spread: {e}")
            return None
        except asyncio.TimeoutError:
             logger.error(f"Timeout fetching spread for Spot: {spot_market}, Perps: {perp_markets}")
             return None
        except Exception as e:
            logger.error(f"Unexpected error fetching spread: {e}", exc_info=True)
            return None