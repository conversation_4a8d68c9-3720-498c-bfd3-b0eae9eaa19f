from typing import Dict, List
import pandas as pd
import os
import logging
import time
import asyncio
import aiohttp
from decimal import Decimal
from functools import partial
from datetime import datetime, timedelta, timezone

try:
    from .exchange_interface import ExchangeInterface, Balance, Position
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from exchanges.exchange_interface import ExchangeInterface, Balance, Position

try:
    from hyperliquid.info import Info
    from hyperliquid.exchange import Exchange
    from hyperliquid.utils import constants
    from eth_account import Account
    from eth_account.signers.local import LocalAccount
except ImportError:
    logging.error("hyperliquid-python-sdk or eth_account not found. Install with: pip install hyperliquid-python-sdk eth_account")
    raise

logger = logging.getLogger(__name__)

# API Key Expiration Monitoring
API_EXPIRATION_DATE = datetime(2026, 1, 18)  # 1/18/2026
WARNING_THRESHOLD_DAYS = 21  # 3 weeks
CRITICAL_THRESHOLD_DAYS = 3   # 3 days

# Add class-level error tracking for throttling
_last_error_messages = {}
_error_throttle_time = 300  # 5 minutes in seconds

def should_log_error(error_key: str, current_time: float) -> bool:
    """Check if we should log this error or if it's been throttled."""
    if error_key not in _last_error_messages:
        _last_error_messages[error_key] = current_time
        return True
    
    time_since_last = current_time - _last_error_messages[error_key]
    if time_since_last >= _error_throttle_time:
        _last_error_messages[error_key] = current_time
        return True
    
    return False

def check_api_key_expiration():
    """
    Check API key expiration and implement graduated warnings/slowdowns.
    
    Better solution ideas:
    1. Auto-renewal: Generate new API key before expiration
    2. Multiple fallback keys: Rotate between multiple API keys
    3. Email/Slack alerts: External notification system
    4. Dashboard indicator: Visual warning in UI
    5. Graceful degradation: Switch to read-only mode before full failure
    """
    now = datetime.now()
    days_until_expiration = (API_EXPIRATION_DATE - now).days
    
    if days_until_expiration <= 0:
        logger.critical("🚨 HYPERLIQUID API KEY HAS EXPIRED! Trading functionality disabled.")
        raise RuntimeError("Hyperliquid API key has expired. Trading cannot proceed.")
    
    elif days_until_expiration <= CRITICAL_THRESHOLD_DAYS:
        logger.critical(f"🚨 CRITICAL: Hyperliquid API key expires in {days_until_expiration} days! "
                       f"Expires on {API_EXPIRATION_DATE.strftime('%Y-%m-%d')}. "
                       "Adding deliberate slowdown to force attention.")
        # Deliberate slowdown - noticeable but not crippling
        time.sleep(2.0)  # 2 second delay on critical operations
        
    elif days_until_expiration <= WARNING_THRESHOLD_DAYS:
        logger.warning(f"⚠️  WARNING: Hyperliquid API key expires in {days_until_expiration} days! "
                      f"Expires on {API_EXPIRATION_DATE.strftime('%Y-%m-%d')}. "
                      "Please renew API key soon.")
    
    return days_until_expiration

class HyperliquidExchange(ExchangeInterface):
    def __init__(self):
        # Check for environment variables for API credentials
        self.api_secret = os.getenv('HYPERLIQUID_MIDAS_API_SECRET')
        self.api_wallet = os.getenv('HYPERLIQUID_MIDAS_API_WALLET') 
        self.control_wallet = os.getenv('HYPERLIQUID_MIDAS_CONTROL_WALLET')
        
        # Temporary check for fresh environment variables
        if self.api_wallet and self.api_wallet.startswith('0x01C02'):
            logger.info("Detected old API wallet - refreshing environment variables")
            # Look specifically for the new Hyperliquid variables
            try:
                with open('/etc/environment', 'r') as f:
                    lines = f.readlines()
                    
                for i, line in enumerate(lines):
                    if 'HYPERLIQUID_MIDAS_API_WALLET="0x08Ce' in line:
                        # Found the target wallet line, parse it
                        if '=' in line:
                            key, value = line.strip().split('=', 1)
                            # Remove surrounding double quotes
                            if value.startswith('"') and value.endswith('"'):
                                value = value[1:-1]
                            os.environ[key] = value
                            logger.info(f"Updated {key}")
                        
                        # Check if next line is the secret
                        if i + 1 < len(lines) and 'HYPERLIQUID_MIDAS_API_SECRET="' in lines[i + 1]:
                            secret_line = lines[i + 1]
                            if '=' in secret_line:
                                key, value = secret_line.strip().split('=', 1)
                                # Remove surrounding double quotes
                                if value.startswith('"') and value.endswith('"'):
                                    value = value[1:-1]
                                os.environ[key] = value
                                logger.info(f"Updated {key}")
                        break
                
                # Re-read environment variables
                self.api_secret = os.getenv('HYPERLIQUID_MIDAS_API_SECRET')
                self.api_wallet = os.getenv('HYPERLIQUID_MIDAS_API_WALLET') 
                self.control_wallet = os.getenv('HYPERLIQUID_MIDAS_CONTROL_WALLET')
                logger.info(f"After refresh, API wallet is: {self.api_wallet}")
            except Exception as e:
                logger.warning(f"Failed to refresh environment variables: {e}")
        else:
            logger.info("Environment variables successfully persist, you may remove this check.")
        
        # Check API key expiration on initialization
        check_api_key_expiration()
        
        # Info class doesn't require credentials for read-only operations
        self.info = Info()
        
        # Exchange class requires credentials for trading operations
        if self.api_secret and self.control_wallet:
            try:
                # Create LocalAccount from API wallet's private key
                wallet: LocalAccount = Account.from_key(self.api_secret)
                
                # Initialize Exchange with API wallet and control wallet address
                self.exchange = Exchange(
                    wallet=wallet,
                    base_url=constants.MAINNET_API_URL,
                    account_address=self.control_wallet
                )
                logger.info("Hyperliquid exchange initialized with trading credentials")
            except Exception as e:
                logger.warning(f"Failed to initialize Hyperliquid exchange client: {e}")
                self.exchange = None
        else:
            logger.info("Hyperliquid credentials not found. Read-only mode (no trading).")
            self.exchange = None
            
        self.collateral_pool_name = "hyperliquid"
        self.instrument_standardized_info = []
        self._last_error_log_time: Dict[str, float] = {}
        self.valid_perp_symbols: List[str] = []

    def supports_funding_rates(self) -> bool:
        """Hyperliquid supports funding rates for perpetual futures"""
        return True

    async def initialize_standardized_instrument_info(self):
        """Initialize standardized instrument information."""
        self.instrument_standardized_info = await self.get_standardized_instrument_info()
        await self._get_valid_perp_symbols()
        
    async def update_standardized_instrument_info(self):
        """Update the standardized instrument information if needed."""
        self.instrument_standardized_info = await self.get_standardized_instrument_info()

    def convert_to_spot_standardized_symbol(self, exchange_symbol: str) -> str:
        """Convert exchange-specific spot symbol to standardized format."""
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_spot'):
                return instrument.get('standardized_format_symbol')
        
        logger.error(f"No spot symbol found for {exchange_symbol}.")
        return ""

    def convert_to_perp_standardized_symbol(self, exchange_symbol: str) -> str:
        """Convert exchange-specific perpetual symbol to standardized format."""        
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_perpetual'):
                return instrument.get('standardized_format_perp')
        
        logger.error(f"No perp symbol found for {exchange_symbol}. This should not happen.")
        return ""
    
    def convert_to_spot_exchange_symbol(self, standardized_symbol: str) -> str:
        """Convert standardized spot symbol to exchange-specific format."""
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_symbol') == standardized_symbol and instrument.get('is_spot'):
                return instrument.get('exchange_format_symbol')
        
        logger.error(f"No spot symbol found for {standardized_symbol}.")
        return ""
    
    def convert_to_perp_exchange_symbol(self, standardized_symbol: str) -> str:
        """Convert standardized perpetual symbol to exchange-specific format."""
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_perp') == standardized_symbol and instrument.get('is_perpetual'):
                return instrument.get('exchange_format_symbol')
        
        logger.error(f"No perp symbol found for {standardized_symbol}.")
        return ""

    async def get_standardized_instrument_info(self):
        """Get standardized instrument information from Hyperliquid."""
        try:
            loop = asyncio.get_running_loop()
            
            # Use run_in_executor for synchronous SDK calls - get both perp and spot metadata
            perp_meta_task = loop.run_in_executor(None, self.info.meta)
            spot_meta_task = loop.run_in_executor(None, self.info.spot_meta)

            perp_meta_response, spot_meta_response = await asyncio.gather(
                perp_meta_task, spot_meta_task
            )

            if not perp_meta_response and not spot_meta_response:
                logger.error("Invalid or empty response for instruments info from Hyperliquid.")
                return []

        except Exception as e:
            logger.error(f"Error fetching instrument info from Hyperliquid: {str(e)}", exc_info=True)
            return []

        hyperliquid_instruments = []
        
        # Process perpetual instruments using documented fields
        if perp_meta_response and 'universe' in perp_meta_response:
            perp_instruments = perp_meta_response['universe']
            for instrument in perp_instruments:
                try:
                    # Extract documented fields
                    name = instrument.get('name', '')
                    sz_decimals = instrument.get('szDecimals', 0)
                    max_leverage = instrument.get('maxLeverage', 50)
                    only_isolated = instrument.get('onlyIsolated', False)
                    is_delisted = instrument.get('isDelisted', False)
                    
                    # Skip delisted instruments
                    if is_delisted:
                        continue
                    
                    # Hyperliquid perpetuals are USD-margined
                    base_coin = name
                    quote_coin = "USD"
                    
                    # Calculate tick size using Hyperliquid's documented rule:
                    # Price precision = MAX_DECIMALS - szDecimals, where MAX_DECIMALS = 6 for perpetuals
                    MAX_DECIMALS_PERP = 6
                    price_decimal_places = MAX_DECIMALS_PERP - sz_decimals
                    tick_size = 10 ** (-price_decimal_places)
                    
                    hyperliquid_instruments.append({
                        'exchange': 'hyperliquid',
                        'is_spot': False,
                        'is_perpetual': True,
                        'is_dated_future': False,
                        'exchange_format_symbol': name,
                        'base_coin': base_coin,
                        'quote_coin': quote_coin,
                        'standardized_format_symbol': f"{base_coin}-{quote_coin}",
                        'standardized_format_perp': f"{base_coin}-{quote_coin}.PERP",
                        'base_precision': 10**(-sz_decimals),
                        'quote_precision': tick_size,
                        'tick_size': tick_size,
                        'exchange_unique_field_list': {
                            'sz_decimals': sz_decimals,
                            'max_leverage': max_leverage,
                            'only_isolated': only_isolated,
                            'is_delisted': is_delisted,
                            'price_decimal_places': price_decimal_places,
                        }
                    })
                    
                except Exception as e:
                    logger.warning(f"Error processing perp instrument {instrument}: {e}")
                    continue
        
        # Process spot instruments using documented fields
        if spot_meta_response:
            universe = spot_meta_response.get('universe', [])
            tokens = spot_meta_response.get('tokens', [])
            
            for market in universe:
                try:
                    # Extract documented fields
                    name = market.get('name', '')
                    token_indices = market.get('tokens', [])
                    market_index = market.get('index', 0)
                    is_canonical = market.get('isCanonical', False)
                    
                    # Accept both canonical and non-canonical markets with valid names
                    if not name:
                        continue
                    
                    # Handle both formats: "PURR/USDC" (canonical) and "@1" (non-canonical)
                    if '/' in name:
                        base_coin, quote_coin = name.split('/')
                    elif name.startswith('@') and token_indices and len(token_indices) >= 2:
                        # For @1, @2 format, get token names from indices
                        base_token_index = token_indices[0]
                        quote_token_index = token_indices[1]
                        
                        base_coin = None
                        quote_coin = None
                        
                        for token in tokens:
                            if token.get('index') == base_token_index:
                                base_coin = token.get('name', '')
                            if token.get('index') == quote_token_index:
                                quote_coin = token.get('name', '')
                        
                        if not base_coin or not quote_coin:
                            continue
                    else:
                        continue
                    
                    # Get base token sz_decimals from tokens array
                    sz_decimals = 6  # Default
                    if token_indices and len(token_indices) > 0:
                        base_token_index = token_indices[0]
                        for token in tokens:
                            if token.get('index') == base_token_index:
                                sz_decimals = token.get('szDecimals', 6)
                                break
                    
                    # Calculate tick size using Hyperliquid's documented rule:
                    # Price precision = MAX_DECIMALS - szDecimals, where MAX_DECIMALS = 8 for spot
                    MAX_DECIMALS_SPOT = 8
                    price_decimal_places = MAX_DECIMALS_SPOT - sz_decimals
                    tick_size = 10 ** (-price_decimal_places)
                    
                    hyperliquid_instruments.append({
                        'exchange': 'hyperliquid',
                        'is_spot': True,
                        'is_perpetual': False,
                        'is_dated_future': False,
                        'exchange_format_symbol': name,
                        'base_coin': base_coin,
                        'quote_coin': quote_coin,
                        'standardized_format_symbol': f"{base_coin}-{quote_coin}",
                        'standardized_format_perp': f"{base_coin}-USD.PERP",
                        'base_precision': 10**(-sz_decimals),
                        'quote_precision': tick_size,
                        'tick_size': tick_size,
                        'exchange_unique_field_list': {
                            'sz_decimals': sz_decimals,
                            'market_index': market_index,
                            'is_canonical': is_canonical,
                            'token_indices': token_indices,
                            'price_decimal_places': price_decimal_places,
                        }
                    })
                    
                except Exception as e:
                    logger.warning(f"Error processing spot market {market}: {e}")
                    continue
        
        return hyperliquid_instruments
    
    async def close_session(self):
        """Closes any underlying sessions."""
        # The Hyperliquid SDK manages its own session
        pass

    async def _get_valid_perp_symbols(self):
        """Retrieves and caches valid perpetual symbols from Hyperliquid."""
        if not self.instrument_standardized_info:
            logger.warning("Instrument info not initialized. Cannot fetch valid perp symbols.")
            return []

        valid_symbols = [
            item['exchange_format_symbol'] 
            for item in self.instrument_standardized_info 
            if item.get('is_perpetual', False)
        ]
        
        self.valid_perp_symbols = valid_symbols
        return valid_symbols
    
    def _get_current_price(self, exchange_symbol: str) -> float:
        """Get current price for a symbol in exchange format (sync wrapper for async method)"""
        return asyncio.run(self.__get_current_price_async(exchange_symbol))

    async def __get_current_price_async(self, exchange_symbol: str) -> float:
        """Async helper for getting current price."""
        async with aiohttp.ClientSession() as session:
            return await self._get_price_with_subscription("hyperliquid", exchange_symbol, session)
    
    async def _get_price_with_subscription(self, exchange_name: str, symbol: str, session: aiohttp.ClientSession) -> float:
        """
        Get current price for a symbol, with fallback to direct Hyperliquid API.
        
        First tries to get price from data service (if supported), then falls back to direct API.
        """
        # First try the standard data service approach
        price = await super()._get_price_with_subscription(exchange_name, symbol, session)
        
        # If data service didn't provide a valid price, fallback to direct Hyperliquid API
        if price <= 0:
            logger.info(f"Data service didn't provide price for {symbol}, falling back to direct Hyperliquid API")
            try:
                # Get current prices from Hyperliquid API directly
                loop = asyncio.get_event_loop()
                all_mids = await loop.run_in_executor(None, self.info.all_mids)
                
                if all_mids and symbol in all_mids:
                    price = float(all_mids[symbol])
                    logger.debug(f"Got price {price} for {symbol} from direct Hyperliquid API")
                else:
                    logger.warning(f"Symbol {symbol} not found in Hyperliquid all_mids response")
                    
            except Exception as e:
                logger.error(f"Error getting price from direct Hyperliquid API for {symbol}: {e}")
        
        return price

    async def _get_funding_data(self, exchange_name: str, symbol: str, session: aiohttp.ClientSession) -> dict:
        """
        Get funding data with fallback to direct Hyperliquid API for spots.
        
        First tries data service (works for perpetuals), then falls back to direct API for spots.
        """
        # First try the standard data service approach
        try:
            funding_data = await super()._get_funding_data(exchange_name, symbol, session)
            if funding_data and funding_data.get('fundingRate') is not None:
                logger.debug(f"Got funding data for {symbol} from data service")
                return funding_data
        except Exception as e:
            logger.debug(f"Data service funding failed for {symbol}: {e}")
        
        # Fallback to direct Hyperliquid API
        logger.info(f"Data service didn't provide funding for {symbol}, falling back to direct Hyperliquid API")
        try:
            # Determine if this is a perpetual or spot symbol
            is_perpetual = '.PERP' in symbol or any(
                inst.get('exchange_format_symbol') == symbol and inst.get('is_perpetual', False) 
                for inst in self.instrument_standardized_info
            )
            
            if not is_perpetual:
                # Spot symbols don't have funding rates
                logger.debug(f"Spot symbol {symbol} doesn't have funding rates on Hyperliquid")
                return {
                    'fundingRate': None,
                    'fundingTimestamp': datetime.now(timezone.utc).isoformat(),
                    'nextFundingTime': None,
                    'lastPrice': None
                }
            
            # Get funding data for perpetuals from Hyperliquid API directly
            loop = asyncio.get_event_loop()
            
            # Convert symbol to hyperliquid format (remove .PERP suffix if present)
            hl_symbol = symbol.replace('.PERP', '').replace('-USD', '')
            
            # Get recent funding history (last hour)
            current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
            start_time = current_time - (60 * 60 * 1000)  # 1 hour ago
            
            funding_history = await loop.run_in_executor(
                None, 
                lambda: self.info.funding_history(hl_symbol, start_time, current_time)
            )
            
            if funding_history:
                latest_funding = funding_history[-1]  # Most recent entry
                logger.debug(f"Got funding rate {latest_funding.get('fundingRate')} for {symbol} from direct Hyperliquid API")
                return {
                    'fundingRate': float(latest_funding.get('fundingRate', 0)),
                    'fundingTimestamp': datetime.fromtimestamp(latest_funding.get('time', current_time) / 1000, timezone.utc).isoformat(),
                    'nextFundingTime': current_time + (60 * 60 * 1000),  # Next hour
                    'lastPrice': None  # Not available in funding history
                }
            else:
                logger.warning(f"No funding history available for {hl_symbol}")
                return {
                    'fundingRate': None,
                    'fundingTimestamp': datetime.now(timezone.utc).isoformat(),
                    'nextFundingTime': None,
                    'lastPrice': None
                }
            
        except Exception as e:
            logger.error(f"Error getting funding from direct Hyperliquid API for {symbol}: {e}")
            return {}

    def get_eligible_collateral_qty(self, standardized_symbol: str, usd_value: float, price: float = None) -> float:
        """
        Calculate theoretical collateral value for Hyperliquid.
        
        Hyperliquid uses cross-margining with USDC as the primary collateral currency.
        """
        # For now, implement basic logic - may need refinement based on Hyperliquid's actual collateral rules
        base_coin = standardized_symbol.split('-')[0]
        
        # USDC is the primary collateral on Hyperliquid
        if base_coin == 'USDC':
            return usd_value
        
        # Other assets may have haircuts - for now assume 80% collateral value
        # This should be updated with actual Hyperliquid collateral rules
        return usd_value * 0.8

    def get_standardized_unified_data(self, nonzero_only: bool = True) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Get unified account data (balances and positions)"""
        try:
            if not self.exchange:
                logger.warning("No exchange client available. Cannot fetch unified data.")
                return pd.DataFrame(), pd.DataFrame()

            # Get user state which includes both balances and positions
            user_state = self.info.user_state(self.control_wallet)
            
            if not user_state:
                logger.error("Empty response for user_state from Hyperliquid")
                return pd.DataFrame(), pd.DataFrame()

            # Extract balance information
            balance_info = user_state.get('marginSummary', {})
            account_value = float(balance_info.get('accountValue', 0))
            total_margin_used = float(balance_info.get('totalMarginUsed', 0))
            total_ntl_pos = float(balance_info.get('totalNtlPos', 0))
            total_raw_usd = float(balance_info.get('totalRawUsd', 0))

            # Summary DataFrame
            summary_df = pd.DataFrame([{
                'exchange': 'hyperliquid',
                'total_balance': account_value,
                'total_margin_balance': account_value,
                'total_initial_margin': total_margin_used,
                'total_available_balance': account_value - total_margin_used,
                'total_unrealized_pnl': 0,  # Will be calculated from positions
                'total_wallet_balance': total_raw_usd,
                'total_maintenance_margin': 0,  # Not directly available in Hyperliquid API
                'total_positions': len(user_state.get('assetPositions', []))
            }])

            # Assets DataFrame (positions in Hyperliquid are the main focus)
            assets = []
            
            # Process asset positions
            asset_positions = user_state.get('assetPositions', [])
            
            for position in asset_positions:
                try:
                    # Extract position data
                    position_info = position.get('position', {})
                    coin = position_info.get('coin', '')
                    
                    if not coin:
                        continue
                        
                    # Get position size and entry price
                    szi = float(position_info.get('szi', 0))  # Position size
                    entry_px = float(position_info.get('entryPx', 0)) if position_info.get('entryPx') else 0
                    
                    # Get current mark price
                    unrealized_pnl = float(position_info.get('unrealizedPnl', 0))
                    
                    # For Hyperliquid, we need to get the mark price from all_mids
                    mark_price = 0
                    try:
                        all_mids = self.info.all_mids()
                        mark_price = float(all_mids.get(coin, 0))
                    except Exception as e:
                        logger.warning(f"Could not get mark price for {coin}: {e}")
                    
                    # Calculate USD values
                    position_usd = szi * mark_price if mark_price > 0 else 0
                    
                    # In Hyperliquid, there's no separate spot balance - everything is in the margin account
                    balance_usd = 0  # No separate spot balance
                    wallet_balance = 0  # No separate wallet balance for individual coins
                    
                    if nonzero_only and szi == 0:
                        continue

                    assets.append({
                        'symbol': coin,
                        'walletBalance': wallet_balance,
                        'locked': 0,  # Not separately tracked in Hyperliquid
                        'position_usd': position_usd,
                        'balance_usd': balance_usd,
                        'total_position_size': szi,
                        'unrealized_pnl': unrealized_pnl,
                        'mark_price': mark_price,
                        'entry_price': entry_px
                    })
                    
                except Exception as e:
                    logger.warning(f"Error processing position {position}: {e}")
                    continue

            # Add USDC balance (main collateral currency)
            try:
                usdc_balance = total_raw_usd  # This represents the USDC balance
                if usdc_balance > 0 or not nonzero_only:
                    assets.append({
                        'symbol': 'USDC',
                        'walletBalance': usdc_balance,
                        'locked': 0,
                        'position_usd': 0,
                        'balance_usd': usdc_balance,
                        'total_position_size': 0,
                        'unrealized_pnl': 0,
                        'mark_price': 1.0,
                        'entry_price': 1.0
                    })
            except Exception as e:
                logger.warning(f"Error adding USDC balance: {e}")

            assets_df = pd.DataFrame(assets) if assets else pd.DataFrame(
                columns=['symbol', 'walletBalance', 'locked', 'position_usd', 'balance_usd', 'total_position_size', 'unrealized_pnl', 'mark_price', 'entry_price']
            )

            logger.info(f"Hyperliquid assets list: {assets}")
            return summary_df, assets_df

        except Exception as e:
            logger.error(f"Error getting Hyperliquid unified data: {str(e)}", exc_info=True)
            return pd.DataFrame(), pd.DataFrame()
    
    def get_balances(self, nonzero_only: bool = True) -> List[Balance]:
        """Get account balances"""
        # Check API key expiration before making requests
        check_api_key_expiration()
        
        try:
            if not self.exchange:
                logger.warning("No exchange client available. Cannot fetch balances.")
                return []

            user_state = self.info.user_state(self.control_wallet)
            if not user_state:
                logger.error("Invalid response format from Hyperliquid")
                return []
                
            balances = []
            
            # Get total USD balance (USDC is the main collateral)
            margin_summary = user_state.get('marginSummary', {})
            total_raw_usd = float(margin_summary.get('totalRawUsd', 0))
            
            if total_raw_usd > 0 or not nonzero_only:
                balances.append(Balance(
                    symbol='USDC',
                    total=total_raw_usd,
                    available=total_raw_usd - float(margin_summary.get('totalMarginUsed', 0)),
                    in_order=float(margin_summary.get('totalMarginUsed', 0))
                ))
                
            return balances
            
        except Exception as e:
            logger.error(f"Error getting Hyperliquid balances: {str(e)}", exc_info=True)
            return []
    
    def get_positions(self, nonzero_only: bool = True) -> List[Position]:
        """Get open positions"""
        try:
            if not self.exchange:
                logger.warning("No exchange client available. Cannot fetch positions.")
                return []

            user_state = self.info.user_state(self.control_wallet)
            if not user_state:
                logger.error("Invalid response format from Hyperliquid")
                return []
                
            positions = []
            asset_positions = user_state.get('assetPositions', [])
            
            for asset_position in asset_positions:
                position_info = asset_position.get('position', {})
                coin = position_info.get('coin', '')
                szi = float(position_info.get('szi', 0))
                
                if nonzero_only and szi == 0:
                    continue
                    
                # Convert to standardized symbol format
                standardized_symbol = f"{coin}-USD.PERP"
                
                entry_price = float(position_info.get('entryPx', 0)) if position_info.get('entryPx') else 0
                unrealized_pnl = float(position_info.get('unrealizedPnl', 0))
                
                # Get mark price
                mark_price = 0
                try:
                    all_mids = self.info.all_mids()
                    mark_price = float(all_mids.get(coin, 0))
                except Exception as e:
                    logger.warning(f"Could not get mark price for {coin}: {e}")
                
                positions.append(Position(
                    symbol=standardized_symbol,
                    size=szi,
                    entry_price=entry_price,
                    mark_price=mark_price,
                    unrealized_pnl=unrealized_pnl
                ))
        
            return positions
            
        except Exception as e:
            logger.error(f"Error getting Hyperliquid positions: {str(e)}", exc_info=True)
            return []
            
    def get_mark_prices(self, standardized_symbols: List[str]) -> Dict[str, float]:
        """Get mark prices for given standardized symbols"""
        try:
            # Get all market prices from Hyperliquid
            all_mids = self.info.all_mids()
            mark_prices = {}
            
            for std_symbol in standardized_symbols:
                # Convert standardized symbol back to Hyperliquid format
                if ".PERP" in std_symbol:
                    # Extract base coin from BTC-USD.PERP -> BTC
                    base_coin = std_symbol.split("-")[0]
                    if base_coin in all_mids:
                        mark_prices[std_symbol] = float(all_mids[base_coin])
                else:
                    # For spot symbols (though Hyperliquid doesn't really have spot)
                    base_coin = std_symbol.split("-")[0]
                    if base_coin in all_mids:
                        mark_prices[std_symbol] = float(all_mids[base_coin])
            
            return mark_prices
            
        except Exception as e:
            logger.error(f"Error getting Hyperliquid mark prices: {str(e)}", exc_info=True)
            return {}
            
    def get_tickers(self, category: str) -> Dict:
        """Get tickers for a specific category"""
        try:
            # Hyperliquid doesn't have categories like spot/linear - everything is perpetual
            all_mids = self.info.all_mids()
            tickers_dict = {}
            
            for symbol, price in all_mids.items():
                tickers_dict[symbol] = {
                    'symbol': symbol,
                    'lastPrice': str(price),
                    'markPrice': str(price)
                }
            
            return tickers_dict
            
        except Exception as e:
            logger.error(f"Error getting Hyperliquid tickers: {e}")
            return {}

# Initialize client for module-level usage (optional)
try:
    hyperliquid_client = HyperliquidExchange()
except Exception as e:
    logger.error(f"Failed to initialize Hyperliquid client: {e}")
    hyperliquid_client = None