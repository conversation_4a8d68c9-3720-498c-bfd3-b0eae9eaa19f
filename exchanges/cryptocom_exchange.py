from typing import Dict, List
import pandas as pd
import os
import logging
import requests
import hmac
import hashlib
import time
import json
from urllib.parse import urlencode
import aiohttp
import asyncio
import random
from typing import Optional
try:
    from .exchange_interface import ExchangeInterface, Balance, Position
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from exchanges.exchange_interface import ExchangeInterface, Balance, Position

logger = logging.getLogger(__name__)

class CryptoComExchange(ExchangeInterface):
    _alerted_zero_haircut_coins = set() # Set to track coins that have triggered the zero haircut alert

    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None):
        """
        Initializes the CryptoComExchange client.
        Optionally accepts API key and secret, falling back to environment variables if not provided.
        """
        super().__init__()
        self.api_key = api_key if api_key is not None else os.getenv('CRYPTOCOM_QD_READ_API_KEY')
        self.api_secret = api_secret if api_secret is not None else os.getenv('CRYPTOCOM_QD_READ_API_SECRET')
        if not self.api_key or not self.api_secret:
            logger.warning("Crypto.com API credentials not found. Set CRYPTOCOM_QD_READ_API_KEY and CRYPTOCOM_QD_READ_API_SECRET environment variables or pass them during initialization.")
            raise ValueError("Missing Crypto.com API credentials")

        self.base_url = "https://api.crypto.com/exchange"
        self.session = None  # Will be initialized as an aiohttp.ClientSession
        self.instrument_standardized_info: List[Dict] = []
        self._symbol_mappings = {}
        self._risk_parameters: Dict[str, Dict] = {}
        self._risk_params_last_updated: float = 0
        self.collateral_pool_name = "cryptocom"

    def supports_funding_rates(self) -> bool:
        """Crypto.com supports funding rates for perpetual futures"""
        return True

    async def initialize_standardized_instrument_info(self):
        """Initialize standardized instrument information."""
        if not self.session or self.session.closed:
            self.session = aiohttp.ClientSession()
        
        self.instrument_standardized_info = await self.get_standardized_instrument_info()
        self._create_symbol_mappings()

    async def update_standardized_instrument_info(self):
        """Update the standardized instrument information if needed."""
        if not self.session or self.session.closed:
            self.session = aiohttp.ClientSession()
        self.instrument_standardized_info = await self.get_standardized_instrument_info()
        self._create_symbol_mappings()

    def convert_to_exchange_specific_stable_suffix(self, standardized_symbol: str) -> str:
        # we are usually going to want usd, so convert there. This forces us to be explicit if we want to pass in usdc or usdt as these are not common for perps
        if "-USDT" in standardized_symbol:
            return standardized_symbol.replace("-USDT","-USD")
        elif "-USDC" in standardized_symbol:
            return standardized_symbol.replace("-USDC","-USD")
        else:
            return standardized_symbol
    
    def convert_to_spot_standardized_symbol(self, exchange_symbol: str) -> str:
        """Convert exchange-specific spot symbol to standardized format."""
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_spot'):
                return instrument.get('standardized_format_symbol')
        
        logger.error(f"No spot symbol found for {exchange_symbol}. returning empty string. Take a look. This should not happen since we should only be inputting a pre-cleared cryptocom symbol into this function")
        return ""
    
    def convert_to_perp_standardized_symbol(self, exchange_symbol: str, skip_exchange_specific_stable_conversion = False) -> str:
        """Convert exchange-specific perpetual symbol to standardized format."""
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_perpetual'):
                return instrument.get('standardized_format_perp')

        logger.error(f"No perp symbol found for {exchange_symbol}. returning empty string. Take a look. This should not happen")
        if skip_exchange_specific_stable_conversion:
            logger.warning("are you sure you wanted to explicitly skip the conversion from USDT to USD?")
        return ""
    
    def convert_to_spot_exchange_symbol(self, standardized_symbol: str) -> str:
        """Convert standardized spot symbol to exchange-specific format."""
        # Simply look up in instrument info
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_symbol') == standardized_symbol and instrument.get('is_spot'):
                return instrument.get('exchange_format_symbol')

        logger.error(f"No spot symbol found for {standardized_symbol}. returning empty string. Take a look. This should not happen since we should only be inputting a pre-cleared cryptocom symbol into this function")
        return ""

    def convert_to_perp_exchange_symbol(self, standardized_symbol: str, skip_exchange_specific_stable_conversion = False) -> str:
        """Convert standardized perpetual symbol to exchange-specific format."""
        # Look up in instrument info
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_perp') == standardized_symbol and instrument.get('is_perpetual'):
                return instrument.get('exchange_format_symbol')

        logger.error(f"No perp symbol found for {standardized_symbol}. returning empty string. Take a look.")
        if skip_exchange_specific_stable_conversion:
            logger.warning("are you sure you wanted to explicitly skip the conversion from USDT to USD?")
        return ""

    def convert_base_coin_to_spot_exchange_symbol(self, base_symbol: str) -> str:
        """Convert base coin to spot exchange symbol."""
        for instrument in self.instrument_standardized_info:
            if instrument.get('base_symbol') == base_symbol and instrument.get('is_spot'):
                return instrument.get('exchange_format_symbol')

    def _create_symbol_mappings(self):
        """Create mappings between standardized and exchange-specific symbols"""
        # Ensure instrument_standardized_info is populated before creating mappings
        if not self.instrument_standardized_info:
            logger.warning("Instrument info is not populated. Cannot create symbol mappings.")
            return

        self.standardized_to_exchange = {}
        self.exchange_to_standardized = {}
        self.standardized_perp_to_exchange = {}

        for instrument in self.instrument_standardized_info:
            standardized_symbol = instrument.get('standardized_format_symbol')
            standardized_perp = instrument.get('standardized_format_perp')
            exchange_symbol = instrument.get('exchange_format_symbol')
            is_spot = instrument.get('is_spot', True)
            is_perpetual = instrument.get('is_perpetual', False)
            is_dated_future = instrument.get('is_dated_future', False)

            if exchange_symbol:
                # Map exchange format to standardized format
                is_spot = instrument.get('is_spot', True)
                target = standardized_symbol if is_spot else standardized_perp
                self.exchange_to_standardized[exchange_symbol] = target

                # Map standardized format to exchange format
                if standardized_symbol:
                    self.standardized_to_exchange[standardized_symbol] = exchange_symbol
                if standardized_perp:
                    self.standardized_perp_to_exchange[standardized_perp] = exchange_symbol

    def _sign_request(self, method: str, params: dict = None) -> tuple[dict, str, dict]:
        """Sign a request using the API credentials"""
        nonce = int(time.time() * 1000)  # Unix timestamp in milliseconds

        # Prepare request data
        request_data = {
            "id": nonce,  # Use nonce as request ID
            "method": method,
            "api_key": self.api_key,
            "params": params or {},
            "nonce": nonce
        }

        def params_to_str(obj):
            if obj is None:
                return ""
            if isinstance(obj, (str, int, float)):
                return str(obj)
            if isinstance(obj, list):
                return "".join(params_to_str(item) for item in obj)
            if isinstance(obj, dict):
                return "".join(key + params_to_str(obj[key]) for key in sorted(obj.keys()))
            return ""

        # Generate signature string according to docs:
        # method + id + api_key + paramsString + nonce
        params_str = params_to_str(request_data["params"])
        sign_str = method + str(nonce) + self.api_key + params_str + str(nonce)
        
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            sign_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        request_data["sig"] = signature  # Add signature to request body
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        request_path = f"/v1/{method}"
        if method.startswith('public/') and params:
            sorted_params = dict(sorted(params.items()))
            params_str = urlencode(sorted_params)
            request_path = f"{request_path}?{params_str}"
        
        return headers, request_path, request_data
        
    def _make_request(self, method: str, params: dict = None) -> dict:
        """Make an authenticated request to the Crypto.com Exchange API"""
        headers, request_path, request_data = self._sign_request(method, params)
        url = f"{self.base_url}{request_path}"
        
        try:
            if method.startswith('public/'):
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers, json=request_data)
                
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') != 0:  # Crypto.com uses 0 for success
                logger.error(f"API error: {data.get('msg', 'Unknown error')}")
                return {}

            logger.info(f"Crypto.com API response: {json.dumps(data)}")
            return data

        except aiohttp.ClientResponseError as e:
            logger.error(f"HTTP error making request to Crypto.com API: {e.status} - {e.message}")
            return {}
        except aiohttp.ClientConnectorError as e:
            logger.error(f"Connection error making request to Crypto.com API: {e}")
            return {}
        except Exception as e:
            logger.error(f"An unexpected error occurred making request to Crypto.com API: {str(e)}")
            return {}
            
    def get_balances(self, nonzero_only: bool = True) -> List[Balance]:
        """Get account balances"""
        try:
            response = self._make_request('private/user-balance')
            if not response or 'result' not in response or 'data' not in response['result']:
                logger.error("Invalid response format from Crypto.com")
                return []

            balances = []
            for account_data in response['result']['data']:
                for position in account_data.get('position_balances', []):
                    quantity = float(position.get('quantity', 0))
                    if nonzero_only and quantity == 0:
                        continue
                        
                    balances.append(Balance(
                        symbol=position.get('instrument_name', ''),
                        total=quantity,
                        available=float(position.get('max_withdrawal_balance', 0)),
                        in_order=quantity - float(position.get('max_withdrawal_balance', 0))
                    ))

            return balances

        except Exception as e:
            logger.error(f"Error getting Crypto.com balances: {str(e)}", exc_info=True)
            return []
            
    def get_positions(self, nonzero_only: bool = True) -> List[Position]:
        """Get open positions"""
        try:
            response = self._make_request('private/get-positions')
            if not response or 'result' not in response:
                logger.error("Invalid response format from Crypto.com")
                return []

            positions = []
            for pos in response['result'].get('data', []):
                size = float(pos.get('quantity', 0))
                if nonzero_only and size == 0:
                    continue

                # Calculate entry_price: cost / size if size is not zero, otherwise 0.0
                cost = float(pos.get('cost', 0))
                entry_price = cost / size if size != 0 else 0.0

                positions.append(Position(
                    symbol=pos.get('instrument_name', ''),
                    size=size,
                    entry_price=entry_price,
                    mark_price=0.0, # As per instructions, set mark_price to 0.0
                    unrealized_pnl=float(pos.get('open_position_pnl', 0))
                ))

            return positions

        except Exception as e:
            logger.error(f"Error getting Crypto.com positions: {str(e)}", exc_info=True)
            return []
            
    # def get_standardized_unified_data(self, nonzero_only: bool = True) -> tuple[pd.DataFrame, pd.DataFrame]:
    #     """Get unified account data"""
    #     try:
    #         response = self._make_request('private/user-balance')
            
    #         if not response:
    #             logger.error("Empty response from Crypto.com API")
    #             return pd.DataFrame(), pd.DataFrame()
            
    #         try:
    #             result = response['result']
    #             data = result['data']
                
    #             if not data:
    #                 logger.error("Crypto.com API returned empty data array")
    #                 return pd.DataFrame(), pd.DataFrame()
                    
    #             data = data[0]  # Get first account data
    #             logger.info(f"Crypto.com API response (private/user-balance): {json.dumps(response, indent=2)}")
    #         except KeyError as e:
    #             logger.error(f"Invalid response format from Crypto.com: {e}")
    #             return pd.DataFrame(), pd.DataFrame()
            
    #         try:
    #             total_available_balance = float(data['total_available_balance'])
    #             total_margin_balance = float(data['total_margin_balance'])
    #             total_initial_margin = float(data['total_initial_margin'])
    #             total_maintenance_margin = float(data['total_maintenance_margin'])
    #             total_cash_balance = float(data['total_cash_balance'])
    #             total_unrealized_pnl = float(data.get('total_session_unrealized_pnl', 0))
                
    #             # Get position balances
    #             position_balances = data['position_balances']
    #         except KeyError as e:
    #             logger.error(f"Missing required balance field: {e}")
    #             total_available_balance = 0.0
    #             total_margin_balance = 0.0
    #             total_initial_margin = 0.0
    #             total_maintenance_margin = 0.0
    #             total_cash_balance = 0.0
    #             total_unrealized_pnl = 0.0
    #             position_balances = []
            
    #         summary_df = pd.DataFrame([{
    #             'exchange': 'crypto_com',
    #             'total_balance': total_margin_balance,  # Using margin balance as equivalent to total_balance
    #             'total_margin_balance': total_margin_balance,
    #             'total_initial_margin': total_initial_margin,
    #             'total_available_balance': total_available_balance,
    #             'total_unrealized_pnl': total_unrealized_pnl,
    #             'total_wallet_balance': total_cash_balance,
    #             'total_maintenance_margin': total_maintenance_margin,
    #             'total_positions': len(position_balances)
    #         }])
            
    #         assets = []
    #         for pos in position_balances:
    #             try:
    #                 instrument_name = pos['instrument_name']
                    
    #                 try:
    #                     quantity = float(pos['quantity'])
    #                 except (KeyError, ValueError):
    #                     logger.warning(f"Could not get quantity for {instrument_name}, setting to 0")
    #                     quantity = 0.0
                        
    #                 if nonzero_only and quantity == 0:
    #                     continue
                    
    #                 try:
    #                     market_value = float(pos['market_value'])
    #                 except (KeyError, ValueError):
    #                     logger.warning(f"Could not get market_value for {instrument_name}, setting to 0")
    #                     market_value = 0.0
                    
    #                 try:
    #                     max_withdrawal_balance = float(pos['max_withdrawal_balance'])
    #                 except (KeyError, ValueError):
    #                     logger.warning(f"Could not get max_withdrawal_balance for {instrument_name}, setting to 0")
    #                     max_withdrawal_balance = quantity  # Fallback to quantity
                    
    #                 # For stablecoins, use fixed price of 1.0
    #                 STABLECOINS = ['USDT', 'USDC']
    #                 if instrument_name in STABLECOINS:
    #                     mark_price = 1.0
    #                     position_usd = 0.0  # No position for stablecoins
    #                     balance_usd = quantity  # For stablecoins, USD value equals quantity
    #                 else:
    #                     # Calculate mark price from market_value and quantity
    #                     mark_price = market_value / quantity if quantity != 0 else 0
    #                     position_usd = 0.0  # This is a balance (spot), not a position (futures)
    #                     balance_usd = market_value  # Market value is the USD value of the balance
                        
    #                 assets.append({
    #                     'symbol': instrument_name,
    #                     'walletBalance': quantity,
    #                     'locked': quantity - max_withdrawal_balance,
    #                     'position_usd': position_usd,  # USD value of the perpetual position (0 here)
    #                     'balance_usd': balance_usd,    # USD value of the spot balance
    #                     'total_position_size': 0,      # No perpetual position size
    #                     'unrealized_pnl': 0.0,         # No unrealized P&L
    #                     'mark_price': mark_price,
    #                     'entry_price': 0.0             # No entry price for spot-only
    #                 })
    #             except KeyError as e:
    #                 logger.warning(f"Missing required field in position data: {e}")
    #                 continue
                
    #         assets_df = pd.DataFrame(assets) if assets else pd.DataFrame(
    #             columns=['symbol', 'walletBalance', 'locked', 'position_usd', 
    #                     'balance_usd', 'total_position_size', 'unrealized_pnl', 'mark_price', 'entry_price']
    #         )
            
    #         logger.info(f"Crypto.com assets list: {assets}")
    #         logger.info(f"Crypto.com assets_df columns: {assets_df.columns.tolist()}")
    #         return summary_df, assets_df
    #     except Exception as e:
    #         logger.error(f"Error getting Crypto.com unified data: {str(e)}", exc_info=True)
    #         return pd.DataFrame(), pd.DataFrame()

    def get_mark_prices(self, symbols: List[str] = None) -> Dict[str, float]:
        """Get mark prices for symbols. If no symbols are provided, fetch all."""
        # Special handling for stablecoins - set them to 1.0 directly
        STABLECOINS = ['USDT', 'USDC']

        mark_prices = {}

        # First populate the stablecoins with 1.0
        if symbols:
            for symbol in symbols:
                # Handle both direct stablecoins and compound formats like "USDTUSDT"
                if symbol in STABLECOINS or any(symbol.startswith(s) and symbol.endswith(s) for s in STABLECOINS):
                    mark_prices[symbol] = 1.0
                    logger.info(f"Using fixed price of 1.0 for stablecoin {symbol}")

        try:
            params = {}
            if symbols:
                # Filter out stablecoins from API request
                non_stablecoin_symbols = [s for s in symbols if s not in STABLECOINS and 
                                         not any(s.startswith(stablecoin) and s.endswith(stablecoin) for stablecoin in STABLECOINS)]
                if non_stablecoin_symbols:
                    params['instrument_name'] = ','.join(non_stablecoin_symbols)
                else:
                    # If only stablecoins were requested, we're done
                    return mark_prices
            
            response = self._make_request('public/get-tickers', params)
            
            if not response:
                logger.warning("Empty response from Crypto.com get-tickers API")
                return mark_prices

            try:
                result = response['result']
                data = result['data']

                for item in data:
                    try:
                        # 'i' is instrument_name
                        instrument_name = item['i']

                        # 'a' is the best ask price, 'b' is the best bid price.
                        # Using ('a' + 'b') / 2 as an approximation for mark price.
                        try:
                            ask_price = float(item['a'])
                            bid_price = float(item['b'])
                            mark_price = (ask_price + bid_price) / 2
                            mark_prices[instrument_name] = mark_price
                        except (KeyError, ValueError) as e:
                            logger.warning(f"Could not calculate mark price for {instrument_name}: {e}")
                            mark_prices[instrument_name] = 0
                    except KeyError as e:
                        logger.warning(f"Missing required field in ticker item: {e}")
                        continue
            except KeyError as e:
                logger.warning(f"Invalid response structure from get-tickers: {e}")

        except Exception as e:
            logger.error(f"Error getting Crypto.com mark prices: {str(e)}", exc_info=True)

        return mark_prices

    # def get_collateral_info(self) -> Dict[str, any]:
    #     """
    #     Fetches risk parameters from Crypto.com API and returns a dictionary
    #     where keys are instrument names and values are dictionaries containing
    #     collateral information.
    #     """
    #     try:
    #         response = self._make_request('public/get-risk-parameters')
    #         if not response:
    #             logger.error("Empty response from Crypto.com for get-risk-parameters")
    #             return {}
                
    #         try:
    #             result = response['result']
    #             base_currency_config = result['base_currency_config']
    #         except KeyError as e:
    #             logger.error(f"Invalid response format from Crypto.com for get-risk-parameters: {e}")
    #             return {}

    #         collateral_info = {}
    #         for item in base_currency_config:
    #             try:
    #                 instrument_name = item['instrument_name']
    #                 try:
    #                     unit_margin_rate = item['unit_margin_rate']
    #                 except KeyError:
    #                     unit_margin_rate = '0'
                        
    #                 min_haircut = item.get('minimum_haircut', '1.0')
    #                 collateral_cap = item.get('collateral_cap_notional', None)
                    
    #                 collateral_info[instrument_name] = {
    #                     'unit_margin_rate': unit_margin_rate,
    #                     'minimum_haircut': min_haircut,
    #                     'collateral_cap_notional': collateral_cap
    #                 }
    #             except KeyError:
    #                 # Skip items without instrument_name
    #                 continue
                    
    #         return collateral_info
    #     except Exception as e:
    #         logger.error(f"Error fetching Crypto.com collateral info: {str(e)}", exc_info=True)
    #         return {}
    
    def _get_current_price(self, coin: str) -> float:
        """Get current price for a coin from the local data service API (sync wrapper for async method)"""
        import aiohttp
        import asyncio
        async def fetch():
            async with aiohttp.ClientSession() as session:
                return await self._get_price_with_subscription("crypto-com", coin, session)
        return asyncio.run(fetch())
    
    

    def _fetch_risk_parameters(self, coin: str = None):
        """Fetch risk parameters from Crypto.com API with caching"""
        # Check cache first
        if not hasattr(self, '_risk_params_cache'):
            self._risk_params_cache = {}
            self._risk_params_timestamp = 0
        
        # Refresh cache if it's older than 6 hours
        if time.time() - self._risk_params_timestamp > 21600:  # 6 hours
            self._refresh_risk_parameters()
        if "USD" in coin:
            base_coin = [x['base_coin'] for x in self.instrument_standardized_info if x['standardized_format_symbol'] == coin or x['exchange_format_symbol'] == coin]
            if len(base_coin) > 0:
                coin = base_coin[0]
            else:
                return None
        # Return from cache
        if coin:
            return self._risk_params_cache.get(coin, {})
        else:
            return self._risk_params_cache
    
    def _refresh_risk_parameters(self):
        """Refresh the risk parameters cache"""
        url = f"{self.base_url}/v1/public/get-risk-parameters"
        
        try:
            response = requests.get(url)
            if response.status_code != 200:
                logger.error(f"Error fetching risk parameters: {response.status_code}")
                return
                
            data = response.json()
            if data.get('code') != 0 or 'result' not in data:
                logger.error(f"Invalid response from Crypto.com: {data}")
                return
                
            # Extract and format risk parameters
            base_currency_config = data.get('result', {}).get('base_currency_config', [])
            
            # Update cache
            self._risk_params_cache = {}
            for config in base_currency_config:
                instrument = config.get('instrument_name')
                if instrument:
                    self._risk_params_cache[instrument] = config
                    
            self._risk_params_timestamp = time.time()
            logger.info(f"Refreshed risk parameters cache with {len(self._risk_params_cache)} items")
            
        except Exception as e:
            logger.error(f"Error refreshing risk parameters: {e}")
    
    def get_eligible_collateral_qty(self, coin: str, usd_value: float, price: float = None) -> float:
        """
        Calculate theoretical collateral value for Crypto.com

        Args:
            coin (str): The coin symbol (e.g., 'BTC').
            usd_value (float): The USD value of the coin quantity.
            price (float): The price of the coin in USD.

        Returns:
            float: The eligible collateral quantity in USD.
        """
        # Handle stablecoins directly
        if coin in ['USD', 'USDT', 'USDC']:
            return usd_value

        # Fetch risk parameters for the coin
        params = self._fetch_risk_parameters(coin)

        if not params:
            logger.warning(f"No risk parameters found for {coin}. Not eligible as collateral.")
            return 0.0  # Not eligible as collateral

        min_haircut = float(params.get('minimum_haircut', 1.0))
        unit_margin_rate = float(params.get('unit_margin_rate', 0.0))

        # Special case handling for non-collateral assets
        if min_haircut == 0 and 'collateral_cap_notional' not in params:
            if coin not in self._alerted_zero_haircut_coins:
                logger.warning(f"Coin {coin} has minimum_haircut 0 and no collateral_cap_notional. Not eligible as collateral.")
                self._alerted_zero_haircut_coins.add(coin)
            return 0.0  # Not eligible as collateral

        # Get collateral cap (if exists)
        collateral_cap = float(params.get('collateral_cap_notional', float('inf')))

        # Get current price to calculate quantity - use provided price if available and valid
        if price is None or price <= 0:
            return 0.0 # Cannot calculate collateral without a valid price

        # Calculate quantity based on USD value and price
        qty = usd_value / price

        # Calculate haircut using formula: haircut rate = min(1, max(MHR, UMR*sqrt(qty)))
        # Ensure qty is non-negative for sqrt
        haircut_rate = min(1.0, max(min_haircut, unit_margin_rate * (max(0, qty) ** 0.5)))

        # Apply haircut
        collateral_value = usd_value * (1.0 - haircut_rate)

        # Apply cap
        if collateral_cap != float('inf') and collateral_value > collateral_cap:
            collateral_value = collateral_cap

        return collateral_value
            
    def get_tickers(self, category: str) -> Dict:
        """Get ticker information for instruments of the specified category, standardized with descriptive keys"""
        try:
            response = self._make_request('public/get-tickers')
            if not response or 'result' not in response:
                logger.error("Invalid response format or empty response from Crypto.com API")
                return {}
                
            tickers = {}
            for ticker in response.get('result', {}).get('data', []):
                instrument = ticker.get('i')
                if not instrument:
                    continue

                standardized_ticker = {
                    'symbol': instrument,
                    'lastPrice': ticker.get('a'),
                    'highPrice': ticker.get('h'),
                    'lowPrice': ticker.get('l'),
                    'volume': ticker.get('v'),
                    'quoteVolume': ticker.get('vv'),
                    'openInterest': ticker.get('oi'),
                    'priceChange': ticker.get('c'),
                    'bidPrice': ticker.get('b'),
                    'askPrice': ticker.get('k'),
                    'timestamp': ticker.get('t'),
                }
                tickers[instrument] = standardized_ticker
            return tickers
        except Exception as e:
            logger.error(f"Exception fetching tickers from Crypto.com: {e}", exc_info=True)
            return {}

    def get_standardized_unified_data(self, nonzero_only: bool = True) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Get unified account data including balances and positions, standardized.

        Combines data from private/user-balance (spot balances) and
        private/get-positions (perpetual futures positions). Aggregates
        perpetual positions by base coin to provide a total position size
        and USD value for each underlying asset.
        """
        try:
            # Fetch balances (primarily for spot balances and overall summary data)
            balance_response = self._make_request('private/user-balance')
            logger.info(f"Crypto.com API response (private/user-balance): {balance_response}")
            if not balance_response or 'result' not in balance_response or 'data' not in balance_response['result'] or not balance_response['result']['data']:
                logger.error("Invalid or empty response for user-balance from Crypto.com")
                return pd.DataFrame(), pd.DataFrame()
            balance_data = balance_response['result']['data'][0] # Assuming first element holds the data

            # Fetch positions (for perpetual futures)
            positions_response = self._make_request('private/get-positions')
            logger.info(f"Crypto.com API response (private/get-positions): {positions_response}")
            if not positions_response or 'result' not in positions_response:
                 logger.error("Invalid or empty response for get-positions from Crypto.com")
                 return pd.DataFrame(), pd.DataFrame()

            # Handle both 'data' and 'positions' keys for flexibility
            if 'data' in positions_response['result']:
                positions_data = positions_response['result']['data']
            elif 'positions' in positions_response['result']:
                 positions_data = positions_response['result']['positions']
            else:
                 logger.error("Response missing 'data' or 'positions' key in get-positions result")
                 positions_data = [] # Proceed with empty positions if key is missing

            # --- Summary DataFrame ---
            try:
                summary_df = pd.DataFrame([{
                    'exchange': 'crypto_com',
                    'total_balance': float(balance_data['total_available_balance']),
                    'total_margin_balance': float(balance_data['total_margin_balance']),
                    'total_initial_margin': float(balance_data['total_initial_margin']),
                    'total_available_balance': float(balance_data['total_available_balance']),
                    'total_unrealized_pnl': float(balance_data.get('total_session_unrealized_pnl', 0)),
                    'total_wallet_balance': float(balance_data['total_cash_balance']),
                    'total_maintenance_margin': float(balance_data['total_maintenance_margin']),
                    'total_positions': len([p for p in positions_data if abs(float(p.get('size', 0))) > 1e-9]) # Count non-zero positions
                }])
            except (KeyError, ValueError, TypeError) as e:
                 logger.error(f"Error creating summary DataFrame: {e} - Balance Data: {balance_data}")
                 # Return empty summary but attempt to process assets if balance_data is partially available
                 summary_df = pd.DataFrame()


            # --- Assets Data Processing ---

            # 1. Process Perpetual Positions and aggregate by base coin
            perp_positions_by_base_coin = {}
            logger.info("Processing perpetual position data...")
            for pos in positions_data:
                instrument_name = pos.get('instrument_name')
                if not instrument_name:
                    logger.warning("Skipping position record due to missing 'instrument_name'")
                    continue

                # Find instrument info to get base coin and check if it's a perpetual
                instrument_info = next((item for item in self.instrument_standardized_info if item.get('exchange_format_symbol') == instrument_name), None)

                if not instrument_info or not instrument_info.get('is_perpetual'):
                    # logger.debug(f"Skipping non-perpetual position: {instrument_name}")
                    continue # Skip non-perpetual instruments from this list

                base_coin = instrument_info.get('base_coin')
                standardized_perp_symbol = instrument_info.get('standardized_format_perp')

                if not base_coin or not standardized_perp_symbol:
                     logger.warning(f"Skipping perpetual position {instrument_name} due to missing base_coin or standardized_format_perp in instrument info.")
                     continue

                try:
                    size = float(pos.get('quantity', 0))
                    cost = float(pos.get('cost', 0))
                    unrealized_pnl = float(pos.get('open_position_pnl'))

                except (TypeError, ValueError) as e:
                    logger.error(f"Skipping perpetual position {instrument_name} due to invalid/missing numeric data: {e} - Data: {pos}")
                    continue # Skip this position if critical data is bad

                if nonzero_only and abs(size) < 1e-9: # Use tolerance for float comparison
                    continue

                entry_price = (cost / size) if abs(size) > 1e-9 else 0.0
                # Fetch price using internal service for the specific perpetual symbol
                mark_price = self._get_current_price(instrument_name)
                position_usd = (size * mark_price) if mark_price is not None and mark_price > 0 else 0.0 # Calculate USD value if mark price is valid

                if base_coin not in perp_positions_by_base_coin:
                    perp_positions_by_base_coin[base_coin] = {
                        'symbol': standardized_perp_symbol, # Use standardized perp symbol for the aggregated entry
                        'total_position_size': 0.0,
                        'unrealized_pnl': 0.0,
                        'position_usd': 0.0,
                        'mark_price': mark_price, # Store mark price of the current perp (will be overwritten by subsequent perps of same base coin)
                        'entry_price': entry_price # Store entry price of the current perp (will be overwritten)
                    }

                # Aggregate values for the base coin
                perp_positions_by_base_coin[base_coin]['total_position_size'] += size
                perp_positions_by_base_coin[base_coin]['unrealized_pnl'] += unrealized_pnl
                perp_positions_by_base_coin[base_coin]['position_usd'] += position_usd
                # Optionally update mark_price/entry_price with the latest one processed, or calculate an average/weighted average if needed
                # For simplicity here, we'll just keep the last one processed for this base coin.

            # 2. Process Spot Balances and merge with aggregated perpetual positions
            assets_dict = {}
            logger.info("Processing spot balance data and merging...")
            try:
                # The 'position_balances' list in user-balance contains spot balances
                spot_balances = balance_data.get('position_balances', []) if isinstance(balance_data, dict) else []
            except Exception as e:
                logger.error(f"Error accessing spot balance data for processing: {e}")
                spot_balances = []

            for balance in spot_balances:
                instrument_name = balance.get('instrument_name') # This is the base coin (e.g., 'USDT', 'ETH')
                if not instrument_name:
                    logger.warning("Skipping balance record due to missing 'instrument_name'")
                    continue

                # Use the base coin directly for processing spot balances
                base_coin = instrument_name

                try:
                    total_balance = float(balance.get('quantity', 0))
                    in_order = float(balance.get('reserved_qty', 0))
                    market_value = float(balance.get('market_value', 0)) # USD value provided by API

                except (TypeError, ValueError) as e:
                    logger.error(f"Skipping spot balance {instrument_name} due to invalid/missing numeric data: {e} - Data: {balance}")
                    continue # Skip this balance if critical data is bad

                if nonzero_only and abs(total_balance) < 1e-9 and base_coin not in perp_positions_by_base_coin:
                     continue # Skip zero spot balance only if no existing perpetual position for this base coin

                # Use market_value from the API for balance_usd
                balance_usd = market_value

                # Calculate mark_price for the spot balance from market_value and quantity
                mark_price_for_balance = market_value / total_balance if total_balance != 0 else 0.0

                # Check if there are aggregated perpetual positions for this base coin
                if base_coin in perp_positions_by_base_coin:
                    # Merge spot balance with perpetual position data
                    perp_data = perp_positions_by_base_coin.pop(base_coin) # Get and remove to mark as processed

                    assets_dict[base_coin] = { # Use base coin as the primary symbol for the combined entry
                        'symbol': base_coin, # Use base coin as the symbol
                        'walletBalance': total_balance,
                        'locked': in_order,
                        'position_usd': perp_data['position_usd'],
                        'balance_usd': balance_usd,
                        'total_position_size': perp_data['total_position_size'],
                        'unrealized_pnl': perp_data['unrealized_pnl'],
                        'mark_price': perp_data['mark_price'], # Use mark price from perpetuals
                        'entry_price': perp_data['entry_price'] # Use entry price from perpetuals
                    }
                elif abs(total_balance) > 1e-9 or not nonzero_only:
                    # No perpetual positions for this base coin, add as spot-only asset
                    assets_dict[base_coin] = { # Use base coin as the symbol
                        'symbol': base_coin,
                        'walletBalance': total_balance,
                        'locked': in_order,
                        'position_usd': 0.0, # No perpetual position
                        'balance_usd': balance_usd,
                        'total_position_size': 0.0, # No perpetual position
                        'unrealized_pnl': 0.0, # No perpetual position
                        'mark_price': mark_price_for_balance, # Use calculated spot mark price
                        'entry_price': 0.0 # No entry price for spot-only
                    }

            # 3. Add any remaining perpetual positions (base coins with perps but no spot balance)
            logger.info("Adding remaining perpetual-only assets...")
            for base_coin, perp_data in perp_positions_by_base_coin.items():
                 if abs(perp_data['total_position_size']) > 1e-9 or not nonzero_only:
                    assets_dict[base_coin] = { # Use base coin as the symbol
                        'symbol': base_coin,
                        'walletBalance': 0.0, # No spot balance
                        'locked': 0.0, # No spot balance
                        'position_usd': perp_data['position_usd'],
                        'balance_usd': 0.0, # No spot balance
                        'total_position_size': perp_data['total_position_size'],
                        'unrealized_pnl': perp_data['unrealized_pnl'],
                        'mark_price': perp_data['mark_price'],
                        'entry_price': perp_data['entry_price']
                    }


            # 4. Create Final DataFrame
            final_assets = list(assets_dict.values())
            assets_df = pd.DataFrame(final_assets) if final_assets else pd.DataFrame(
                columns=['symbol', 'walletBalance', 'locked', 'position_usd', 'balance_usd',
                         'total_position_size', 'unrealized_pnl', 'mark_price', 'entry_price']
            )

            # Ensure essential numeric columns exist and are numeric
            essential_cols = ['walletBalance', 'locked', 'position_usd', 'balance_usd',
                              'total_position_size', 'unrealized_pnl', 'mark_price', 'entry_price']
            for col in essential_cols:
                if col not in assets_df.columns:
                    assets_df[col] = 0.0 # Add missing columns with 0.0
                # Convert columns to numeric, coercing errors and filling None/NaN with 0.0
                assets_df[col] = pd.to_numeric(assets_df[col], errors='coerce').fillna(0.0)


            logger.info(f"Crypto.com assets_df columns: {assets_df.columns.tolist()}")
            logger.debug(f"Crypto.com final assets_df head:\n{assets_df.head().to_string()}")
            return summary_df, assets_df

        except Exception as e:
            logger.error(f"Error getting Crypto.com unified data: {str(e)}", exc_info=True)
            return pd.DataFrame(), pd.DataFrame()

    async def get_standardized_instrument_info(self):
        standardized_instruments = []
        
        # Get all instruments including spot and futures
        url = "https://api.crypto.com/exchange/v1/public/get-instruments"
        try:
            if not self.session or self.session.closed:
                self.session = aiohttp.ClientSession()

            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.error(f"HTTP error {response.status} when fetching instruments from Crypto.com")
                    return []
                data = await response.json()
                if data.get("code") == 0 and "result" in data:
                    instruments = data['result']['data']
                    
                    for sym_data in instruments:
                        instrument_info = {
                            'exchange': 'crypto_com',
                            'is_spot': sym_data['inst_type'] == "CCY_PAIR",
                            'is_perpetual': sym_data['inst_type'] == "PERPETUAL_SWAP",
                            'is_dated_future': sym_data['inst_type'] == "FUTURE",
                            'exchange_format_symbol': sym_data['symbol'],
                            'base_coin': sym_data['base_ccy'],
                            'quote_coin': sym_data['quote_ccy'],
                            'standardized_format_symbol': sym_data['base_ccy'] + "-" + sym_data['quote_ccy'],
                            'standardized_format_perp': sym_data['base_ccy'] + "-" + sym_data['quote_ccy'] + '.PERP',
                            'base_precision': 10**-sym_data['quantity_decimals'],
                            'quote_precision': 10**-sym_data['quote_decimals'],
                            'tick_size': sym_data['price_tick_size'],
                            'exchange_unique_field_list': {
                                'margin_trading': sym_data['margin_buy_enabled'] or sym_data['margin_sell_enabled'],
                                'margin_buy_enabled': sym_data['margin_buy_enabled'],
                                'margin_sell_enabled': sym_data['margin_sell_enabled'],
                                'max_leverage': sym_data['max_leverage']
                            }
                        }
                        
                        standardized_instruments.append(instrument_info)
                    
                    logger.info(f"Fetched {len(standardized_instruments)} instruments from Crypto.com")
                    return standardized_instruments
                else:
                    logger.error(f"Failed to fetch instruments: {data}")
                    return []
        except Exception as e:
            logger.error(f"Exception fetching instruments from Crypto.com: {e}")
            return []

    async def close_session(self):
        """Closes the underlying aiohttp session if it exists and is open."""
        if self.session and not self.session.closed:
            await self.session.close()
            logger.info("CryptoComExchange session closed.")
