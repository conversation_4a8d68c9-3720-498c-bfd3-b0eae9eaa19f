# DEPRECATED: This test file uses outdated synchronous methods that have been converted to async.
# Any use of this file needs to consider architecture changes first.
# The CryptoComExchange methods _fetch_risk_parameters and get_eligible_collateral_qty
# have been converted to async versions: _fetch_async_risk_parameters and async get_eligible_collateral_qty

import asyncio
import logging
from typing import Dict
import time
import pandas as pd

from bybit_exchange import BybitExchange
from cryptocom_exchange import CryptoComExchange

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_collateral_tiers():
    """Test collateral tier calculations across different exchanges."""
    try:
        logger.info("Initializing exchanges...")
        
        # Initialize exchanges
        bybit = BybitExchange()
        await bybit.initialize_standardized_instrument_info()
        cryptocom = CryptoComExchange()
        await cryptocom.initialize_standardized_instrument_info()
        
        # Test symbols with different collateral tiers
        test_symbols_bybit = ["BTC-USDT", "ETH-USDT", "SOL-USDT", "USDT-USDT"]
        test_symbols_cryptocom = ["BTC-USDT", "ETH-USDT", "SOL-USDT", "USDT-USDT"]
        test_amounts = [1000, 10000, 100000, 1000000]  # Test with different amounts
        
        # Test Bybit collateral tiers
        logger.info("\n=== TESTING BYBIT COLLATERAL TIERS ===")
        bybit_results = []
        
        for symbol in test_symbols_bybit:
            logger.info(f"\nTesting {symbol} on Bybit:")
            
            # Fetch and log tier information
            exchange_symbol = bybit.convert_to_exchange_symbol(symbol)
            collateral_info = bybit._fetch_collateral_tiers(symbol)
            
            if not collateral_info:
                logger.info(f"  No collateral tier information available for {symbol}")
                continue
                
            if 'collateralRatioList' in collateral_info:
                tiers = collateral_info['collateralRatioList']
                logger.info(f"  Found {len(tiers)} collateral tiers:")
                
                for tier in sorted(tiers, key=lambda x: float(x.get('minQty', 0))):
                    min_qty = tier.get('minQty', 'unknown')
                    max_qty = tier.get('maxQty', 'infinite') if tier.get('maxQty') else 'infinite'
                    ratio = tier.get('collateralRatio', 'unknown')
                    logger.info(f"  • Tier: {min_qty} to {max_qty} - Ratio: {ratio}")
            
            # Test with different USD amounts
            for amount in test_amounts:
                collateral_value = bybit.get_eligible_collateral_qty(symbol, amount)
                haircut_percent = 100 - (collateral_value / amount * 100) if amount > 0 else 0
                
                logger.info(f"  Amount: ${amount}, Collateral: ${collateral_value:.2f}, Haircut: {haircut_percent:.2f}%")
                
                bybit_results.append({
                    'exchange': 'bybit',
                    'symbol': symbol,
                    'amount': amount,
                    'collateral_value': collateral_value,
                    'haircut_percent': haircut_percent
                })
        
        # Test Crypto.com collateral calculations
        logger.info("\n=== TESTING CRYPTO.COM COLLATERAL TIERS ===")
        cryptocom_results = []
        
        for symbol in test_symbols_cryptocom:
            logger.info(f"\nTesting {symbol} on Crypto.com:")
            
            # Fetch and log risk parameters
            exchange_symbol = cryptocom.convert_to_exchange_symbol(symbol)
            
            # For Crypto.com, we need to extract the coin from the symbol
            if '_' in exchange_symbol:
                coin = exchange_symbol.split('_')[0]
            else:
                coin = exchange_symbol
                
            risk_params = cryptocom._fetch_risk_parameters(coin)
            
            if not risk_params:
                logger.info(f"  No risk parameters available for {symbol}")
                continue
                
            min_haircut = risk_params.get('minimum_haircut', 'unknown')
            unit_margin_rate = risk_params.get('unit_margin_rate', 'unknown')
            collateral_cap = risk_params.get('collateral_cap_notional', 'none')
            
            logger.info(f"  Risk Parameters:")
            logger.info(f"  • Minimum Haircut: {min_haircut}")
            logger.info(f"  • Unit Margin Rate: {unit_margin_rate}")
            logger.info(f"  • Collateral Cap: {collateral_cap}")
            
            # Test with different USD amounts
            for amount in test_amounts:
                collateral_value = cryptocom.get_eligible_collateral_qty(symbol, amount)
                haircut_percent = 100 - (collateral_value / amount * 100) if amount > 0 else 0
                
                logger.info(f"  Amount: ${amount}, Collateral: ${collateral_value:.2f}, Haircut: {haircut_percent:.2f}%")
                
                cryptocom_results.append({
                    'exchange': 'crypto_com',
                    'symbol': symbol,
                    'amount': amount,
                    'collateral_value': collateral_value,
                    'haircut_percent': haircut_percent
                })
        
        # Compare results between exchanges
        logger.info("\n=== COLLATERAL COMPARISON SUMMARY ===")
        all_results = bybit_results + cryptocom_results
        results_df = pd.DataFrame(all_results)
        
        for symbol in set(test_symbols_bybit).intersection(test_symbols_cryptocom):
            logger.info(f"\nComparison for {symbol}:")
            symbol_df = results_df[results_df['symbol'] == symbol]
            
            for amount in test_amounts:
                amount_df = symbol_df[symbol_df['amount'] == amount]
                if len(amount_df) == 2:  # We have results from both exchanges
                    bybit_row = amount_df[amount_df['exchange'] == 'bybit'].iloc[0]
                    cryptocom_row = amount_df[amount_df['exchange'] == 'crypto_com'].iloc[0]
                    
                    bybit_value = bybit_row['collateral_value']
                    cryptocom_value = cryptocom_row['collateral_value']
                    
                    logger.info(f"  Amount: ${amount}")
                    logger.info(f"  • Bybit collateral: ${bybit_value:.2f} (haircut: {bybit_row['haircut_percent']:.2f}%)")
                    logger.info(f"  • Crypto.com collateral: ${cryptocom_value:.2f} (haircut: {cryptocom_row['haircut_percent']:.2f}%)")
                    logger.info(f"  • Difference: ${abs(bybit_value - cryptocom_value):.2f} ({abs(bybit_value - cryptocom_value) / amount * 100:.2f}% of amount)")
        
        logger.info("\nCollateral tier tests completed.")
        
    except Exception as e:
        logger.error(f"Error during test: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_collateral_tiers()) 