#!/usr/bin/env python3
import os
import sys
import asyncio
import json
import logging
from pprint import pprint

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from exchanges.bybit_exchange import BybitExchange
from exchanges.cryptocom_exchange import CryptoComExchange

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_exchanges():
    bybit = None
    cryptocom = None
    try:
        # Test Bybit instrument info
        bybit_instruments = []
        try:
            bybit = BybitExchange()
            await bybit.initialize_standardized_instrument_info()
            bybit_instruments = await bybit.get_standardized_instrument_info()
            
            bybit_spot_count = sum(1 for i in bybit_instruments if i['is_spot'])
            bybit_perp_count = sum(1 for i in bybit_instruments if not i['is_spot'])
            
            logger.info(f"Bybit instruments: {len(bybit_instruments)} total")
            logger.info(f"Bybit spot instruments: {bybit_spot_count}")
            logger.info(f"Bybit perpetual instruments: {bybit_perp_count}")
            
            # Print a sample of each type
            for instrument_type in ['spot', 'perp']:
                is_spot = instrument_type == 'spot'
                sample = next((i for i in bybit_instruments if i['is_spot'] == is_spot), None)
                if sample:
                    logger.info(f"\nSample Bybit {instrument_type} instrument:")
                    pprint(sample)
        except Exception as e:
            logger.error(f"Error testing Bybit: {e}")
        
        # Test Crypto.com instrument info
        cryptocom_instruments = []
        try:
            cryptocom = CryptoComExchange()
            await cryptocom.initialize_standardized_instrument_info()
            cryptocom_instruments = await cryptocom.get_standardized_instrument_info()
            
            cryptocom_spot_count = sum(1 for i in cryptocom_instruments if i['is_spot'])
            cryptocom_perp_count = sum(1 for i in cryptocom_instruments if not i['is_spot'])
            
            logger.info(f"\nCrypto.com instruments: {len(cryptocom_instruments)} total")
            logger.info(f"Crypto.com spot instruments: {cryptocom_spot_count}")
            logger.info(f"Crypto.com perpetual instruments: {cryptocom_perp_count}")
            
            # Print a sample of each type
            for instrument_type in ['spot', 'perp']:
                is_spot = instrument_type == 'spot'
                sample = next((i for i in cryptocom_instruments if i['is_spot'] == is_spot), None)
                if sample:
                    logger.info(f"\nSample Crypto.com {instrument_type} instrument:")
                    pprint(sample)
        except Exception as e:
            logger.error(f"Error testing Crypto.com: {e}")
        
        # Test combined instrument info as it would be in AADriver
        try:
            combined_instruments = bybit_instruments + cryptocom_instruments
            logger.info(f"\nCombined instruments: {len(combined_instruments)} total")
            
            # Check if we can find some common trading pairs
            test_pairs = [
                ('BTC', 'USDT', 'bybit'),
                ('ETH', 'USDT', 'bybit'),
                ('BTC', 'USDT', 'crypto_com'),
                ('ETH', 'USDT', 'crypto_com')
            ]
            
            for base, quote, exchange in test_pairs:
                std_symbol = f"{base}-{quote}"
                found = any(i for i in combined_instruments 
                         if i['exchange'] == exchange and 
                         i['standardized_format_symbol'] == std_symbol)
                
                logger.info(f"Found {std_symbol} on {exchange}: {found}")
        except Exception as e:
            logger.error(f"Error testing combined instruments: {e}")
    finally:
        # Ensure sessions are closed
        if bybit and hasattr(bybit, 'close_session'):
            await bybit.close_session()
        if cryptocom and hasattr(cryptocom, 'close_session'):
            await cryptocom.close_session()

if __name__ == "__main__":
    asyncio.run(test_exchanges()) 