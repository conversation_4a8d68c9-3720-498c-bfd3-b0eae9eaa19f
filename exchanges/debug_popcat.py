#!/usr/bin/env python3
import os
import sys
import asyncio
import json
import logging
from pprint import pprint

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from exchanges.bybit_exchange import BybitExchange
from exchanges.cryptocom_exchange import CryptoComExchange

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_popcat():
    # Get all instruments
    try:
        bybit = BybitExchange()
        await bybit.initialize_standardized_instrument_info()
        bybit_instruments = await bybit.get_standardized_instrument_info()
        
        # Look for POPCAT in symbols
        popcat_instruments = [
            i for i in bybit_instruments 
            if 'POPCAT' in i['base_coin'] or 
               'POPCAT' in i['quote_coin'] or 
               'POPCAT' in i['exchange_format_symbol'] or
               'POPCAT' in i['standardized_format_symbol'] or
               'POPCAT' in i['standardized_format_perp']
        ]
        
        if popcat_instruments:
            logger.info(f"Found {len(popcat_instruments)} POPCAT instruments on Bybit:")
            for instrument in popcat_instruments:
                pprint(instrument)
        else:
            logger.info("No POPCAT instruments found on Bybit")
            
        # Log all symbols beginning with P on Bybit
        p_instruments = [
            i for i in bybit_instruments 
            if i['base_coin'].startswith('P')
        ]
        
        if p_instruments:
            logger.info(f"\nFound {len(p_instruments)} instruments with base coin starting with P on Bybit:")
            for instrument in p_instruments[:10]:  # Show just first 10
                logger.info(f"- {instrument['base_coin']}-{instrument['quote_coin']} ({instrument['exchange_format_symbol']})")
                
        # Show total counts
        spot_count = sum(1 for i in bybit_instruments if i['is_spot'])
        perp_count = sum(1 for i in bybit_instruments if not i['is_spot'])
        logger.info(f"\nBybit total instruments: {len(bybit_instruments)}")
        logger.info(f"Bybit spot instruments: {spot_count}")
        logger.info(f"Bybit perpetual instruments: {perp_count}")
            
    except Exception as e:
        logger.error(f"Error in debug script: {e}")

if __name__ == "__main__":
    asyncio.run(debug_popcat()) 