# DEPRECATED: This test file uses outdated synchronous methods that have been converted to async.
# Any use of this file needs to consider architecture changes first.
# The CryptoComExchange methods _fetch_risk_parameters and get_eligible_collateral_qty
# have been converted to async versions: _fetch_async_risk_parameters and async get_eligible_collateral_qty

import asyncio
import time
import logging
from typing import Dict

from exchanges.bybit_exchange import BybitExchange
from exchanges.cryptocom_exchange import CryptoComExchange

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_collateral_info():
    """
    Test fetching collateral parameters and calculating theoretical collateral values.
    """
    # Initialize exchanges
    logger.info("Initializing exchanges...")
    bybit = BybitExchange()
    await bybit.initialize_standardized_instrument_info()
    cryptocom = CryptoComExchange()
    await cryptocom.initialize_standardized_instrument_info()
    
    # Test Bybit collateral info
    logger.info("Testing Bybit collateral parameters...")
    bybit_collateral_info = bybit.get_collateral_info()
    logger.info(f"Bybit collateral info response keys: {bybit_collateral_info.keys() if isinstance(bybit_collateral_info, dict) else 'Not a dict'}")
    
    if 'result' in bybit_collateral_info:
        collateral_list = bybit_collateral_info.get('result', {}).get('list', [])
        logger.info(f"Bybit collateral list count: {len(collateral_list)}")
        
        # Print details for a few sample coins
        for coin_data in collateral_list[:3]:  # First 3 coins
            coin = coin_data.get('currency', 'UNKNOWN')
            logger.info(f"Bybit collateral params for {coin}: {coin_data}")
            
            # Test theoretical collateral calculation
            usd_value = 1000.0  # Simulate holding $1000 worth
            theoretical = bybit._fetch_collateral_tiers(coin)
            logger.info(f"Theoretical collateral tiers for {coin}: {theoretical}")
            
            # Calculate using full method
            theoretical_value = bybit.get_eligible_collateral_qty(coin, usd_value)
            logger.info(f"Theoretical collateral value for ${usd_value} of {coin}: ${theoretical_value:.2f}")
    
    # Test Crypto.com collateral info
    logger.info("\nTesting Crypto.com collateral parameters...")
    cryptocom_collateral_info = cryptocom.get_collateral_info()
    logger.info(f"Crypto.com collateral info count: {len(cryptocom_collateral_info) if isinstance(cryptocom_collateral_info, dict) else 'Not a dict'}")
    
    # Print details for a few sample coins
    sample_coins = list(cryptocom_collateral_info.keys())[:3] if cryptocom_collateral_info else []
    for coin in sample_coins:
        logger.info(f"Crypto.com collateral params for {coin}: {cryptocom_collateral_info[coin]}")
        
        # Test theoretical collateral calculation
        usd_value = 1000.0  # Simulate holding $1000 worth
        risk_params = cryptocom._fetch_risk_parameters(coin)
        logger.info(f"Risk parameters for {coin}: {risk_params}")
        
        # Calculate using full method
        theoretical_value = cryptocom.get_eligible_collateral_qty(coin, usd_value)
        logger.info(f"Theoretical collateral value for ${usd_value} of {coin}: ${theoretical_value:.2f}")
    
    logger.info("Collateral tests completed.")

if __name__ == "__main__":
    asyncio.run(test_collateral_info()) 