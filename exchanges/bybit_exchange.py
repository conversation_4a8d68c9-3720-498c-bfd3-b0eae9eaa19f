from typing import Dict, List
import pandas as pd
import os
import logging
from pybit.unified_trading import HTTP
from pybit.exceptions import InvalidRequestError, FailedRequestError
import requests
import time
import asyncio
import aiohttp
import random
from decimal import Decimal
from functools import partial

try:
    from .exchange_interface import ExchangeInterface, Balance, Position
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from exchanges.exchange_interface import ExchangeInterface, Balance, Position

logger = logging.getLogger(__name__)

# Add class-level error tracking for throttling
_last_error_messages = {}
_error_throttle_time = 300  # 5 minutes in seconds

def should_log_error(error_key: str, current_time: float) -> bool:
    """Check if we should log this error or if it's been throttled."""
    if error_key not in _last_error_messages:
        _last_error_messages[error_key] = current_time
        return True
    
    time_since_last = current_time - _last_error_messages[error_key]
    if time_since_last >= _error_throttle_time:
        _last_error_messages[error_key] = current_time
        return True
    
    return False

class BybitExchange(ExchangeInterface):
    def __init__(self):
        self.api_key = os.getenv('BYBIT_QD_READ_API_KEY')
        self.api_secret = os.getenv('BYBIT_QD_READ_API_SECRET')
        if not self.api_key or not self.api_secret:
            logger.warning("Bybit API credentials not found in environment. Set BYBIT_QD_READ_API_KEY and BYBIT_QD_READ_API_SECRET to enable Bybit functionality.")
            raise ValueError("Missing Bybit API credentials")
            
        self.client = HTTP(
            testnet=False,
            api_key=self.api_key,
            api_secret=self.api_secret
        )
        self.collateral_pool_name = "bybit"
        self.instrument_standardized_info = []
        self._last_error_log_time: Dict[str, float] = {}
        self._collateral_tiers_cache: Dict[str, List[Dict]] = {}
        self._cache_expiry = 3600  # 1 hour
        self._last_cache_update: Dict[str, float] = {}
        self.valid_perp_symbols: List[str] = []

    def supports_funding_rates(self) -> bool:
        """Bybit supports funding rates for perpetual futures"""
        return True

    async def initialize_standardized_instrument_info(self):
        """Initialize standardized instrument information."""
        self.instrument_standardized_info = await self.get_standardized_instrument_info()
        await self._get_valid_perp_symbols()
        
    async def update_standardized_instrument_info(self):
        """Update the standardized instrument information if needed."""
        self.instrument_standardized_info = await self.get_standardized_instrument_info()

    def convert_to_spot_standardized_symbol(self, exchange_symbol: str) -> str:
        """Convert exchange-specific spot symbol to standardized format."""
        # Simply look up in instrument info
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_spot'):
                return instrument.get('standardized_format_symbol')
        
        logger.error(f"No spot symbol found for {exchange_symbol}. returning empty string. Take a look. This should not happen since we should only be inputting a pre-cleared bybit symbol into this function")
        return ""
    # def convert_to_exchange_specific_stable_suffix(self, standardized_symbol: str) -> str:
    #     # we are usually going to want usdt, so convert there. This forces us to be explicit if we want to pass in usdc or usd as these are not common for perps
    #     if "-USDT" in standardized_symbol:
    #         return standardized_symbol
    #     elif "-USDC" in standardized_symbol:
    #         return standardized_symbol.replace("-USDC","-USDT")
    #     else:
    #         return standardized_symbol.replace("-USD","-USDT")
    def convert_to_perp_standardized_symbol(self, exchange_symbol: str) -> str:
        """Convert exchange-specific perpetual symbol to standardized format."""        
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_perpetual'):
                return instrument.get('standardized_format_perp')
        
        logger.error(f"No perp symbol found for {exchange_symbol}. returning empty string. Take a look. This should not happen")
        return ""
    
    def convert_to_spot_exchange_symbol(self, standardized_symbol: str) -> str:
        """Convert standardized spot symbol to exchange-specific format."""
        # Simply look up in instrument info
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_symbol') == standardized_symbol and instrument.get('is_spot'):
                return instrument.get('exchange_format_symbol')
        
        logger.error(f"No spot symbol found for {standardized_symbol}. returning empty string. Take a look. This should not happen since we should only be inputting a pre-cleared bybit symbol into this function")
        return ""
    
    def convert_to_perp_exchange_symbol(self, standardized_symbol: str) -> str:
        """Convert standardized perpetual symbol to exchange-specific format."""
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_perp') == standardized_symbol and instrument.get('is_perpetual'):
                return instrument.get('exchange_format_symbol')
        
        logger.error(f"No perp symbol found for {standardized_symbol}. returning empty string. Take a look.")
        return ""

    async def get_standardized_instrument_info(self):
        try:
            loop = asyncio.get_running_loop()
            
            # Use run_in_executor for synchronous SDK calls with keyword arguments
            bybit_spot_info_task = loop.run_in_executor(
                None, partial(self.client.get_instruments_info, category="spot")
            )
            bybit_linear_perpetuals_info_task = loop.run_in_executor(
                None, partial(self.client.get_instruments_info, category="linear")
            )

            bybit_spot_info_response, bybit_linear_perpetuals_info_response = await asyncio.gather(
                bybit_spot_info_task, bybit_linear_perpetuals_info_task
            )

            if not bybit_spot_info_response or 'result' not in bybit_spot_info_response or 'list' not in bybit_spot_info_response['result']:
                logger.error("Invalid or empty response for spot instruments info from Bybit.")
                bybit_spot_info = []
            else:
                bybit_spot_info = bybit_spot_info_response['result']['list']

            if not bybit_linear_perpetuals_info_response or 'result' not in bybit_linear_perpetuals_info_response or 'list' not in bybit_linear_perpetuals_info_response['result']:
                logger.error("Invalid or empty response for linear perpetuals instruments info from Bybit.")
                bybit_linear_perpetuals_info = []
            else:
                bybit_linear_perpetuals_info = bybit_linear_perpetuals_info_response['result']['list']

        except (InvalidRequestError, FailedRequestError) as e:
            if "Your api key has expired" in str(e) or "ErrCode: 401" in str(e):
                current_time = time.time()
                error_key = "bybit_api_key_expired_instrument_info"
                if should_log_error(error_key, current_time):
                    logger.warning(f"Bybit API key has expired. Cannot fetch instrument info. Error: {str(e)}")
                return []
            else:
                logger.error(f"Request failed while fetching instrument info from Bybit: {str(e)}", exc_info=True)
                return []
        except Exception as e:
            logger.error(f"Error fetching instrument info from Bybit: {str(e)}", exc_info=True)
            return []

        # Process spot instruments
        bybit_standardized_spot_subset = [{'exchange':'bybit',
                                          "is_spot":True,
                                          "is_perpetual":False,
                                          "is_dated_future":False,
                                          'exchange_format_symbol':sym_data['symbol'],
                                          'base_coin':sym_data['baseCoin'],
                                          'quote_coin':sym_data['quoteCoin'],
                                          'standardized_format_symbol':sym_data['baseCoin']+"-"+sym_data['quoteCoin'], 
                                          'standardized_format_perp': sym_data['baseCoin']+"-"+sym_data['quoteCoin']+".PERP", # for matching, obviously this is spot here
                                          'base_precision':sym_data['lotSizeFilter']['basePrecision'],
                                          'quote_precision':sym_data['lotSizeFilter']['quotePrecision'],
                                          'tick_size':sym_data['priceFilter']['tickSize'], # usually USD amount
                                          'exchange_unique_field_list':{
                                              'status':sym_data['status'], # PreLaunch, Trading, Delivering, Closed
                                              'margin_trading':True if sym_data['marginTrading'] in ['both','utaOnly'] else False,
                                              'min_order_quote':sym_data['lotSizeFilter']['minOrderAmt'],
                                              'max_order_quote':sym_data['lotSizeFilter']['maxOrderAmt'],
                                              'min_order_base':sym_data['lotSizeFilter']['minOrderQty'],
                                              'max_order_base':sym_data['lotSizeFilter']['maxOrderQty'],
                                              'st_tag':sym_data['stTag']
                                          }, 
                                         } for sym_data in bybit_spot_info]
        
        # Process perpetual futures instruments
        bybit_perp_instruments = [{'exchange':'bybit',
                                  "is_spot":False,
                                  "is_perpetual":True,
                                  "is_dated_future":False,
                                  'exchange_format_symbol':sym_data['symbol'],
                                  'base_coin':sym_data['baseCoin'],
                                  'quote_coin':sym_data['quoteCoin'],
                                  'standardized_format_symbol':sym_data['baseCoin']+"-"+sym_data['quoteCoin'], 
                                  'standardized_format_perp': sym_data['baseCoin']+"-"+sym_data['quoteCoin']+".PERP",
                                  'base_precision':sym_data['lotSizeFilter']['qtyStep'],
                                  'quote_precision':10**-float(sym_data['priceScale']),  # Default quote precision for futures
                                  'tick_size':sym_data['priceFilter']['tickSize'],
                                  'exchange_unique_field_list':{
                                      'status':sym_data['status'],
                                      'leverage_filter': sym_data.get('leverageFilter', {}),
                                      'min_order_qty': sym_data['lotSizeFilter'].get('minOrderQty', '0'),
                                      'max_order_qty': sym_data['lotSizeFilter'].get('maxOrderQty', '0'),
                                      'funding_interval': sym_data.get('fundingInterval', 480)
                                  }, 
                                 } for sym_data in bybit_linear_perpetuals_info]
        
        # Combine and return both spot and perp instruments
        return bybit_standardized_spot_subset + bybit_perp_instruments
    
    async def close_session(self):
        """Closes the underlying aiohttp session if it exists."""
        # The pybit SDK manages its own session, but if we create any for helpers, close them.
        # This is more of a placeholder for good practice, as the default client might not expose its session.
        pass

    async def _get_valid_perp_symbols(self):
        """
        Retrieves and caches valid perpetual symbols from Bybit.
        """
        if not self.instrument_standardized_info:
            logger.warning("Instrument info not initialized. Cannot fetch valid perp symbols.")
            return []

        valid_symbols = [
            item['exchange_format_symbol'] 
            for item in self.instrument_standardized_info 
            if not item.get('is_spot', True)
        ]
        
        self.valid_perp_symbols = valid_symbols
        return valid_symbols
    
    def _get_current_price(self, exchange_symbol: str) -> float:
        """Get current price for a symbol in exchange format (sync wrapper for async method)"""
        # This is a blocking call, intended to be used in contexts where an event loop isn't running.
        # It's not ideal, but it's a way to bridge sync and async code.
        return asyncio.run(self.__get_current_price_async(exchange_symbol))

    async def __get_current_price_async(self, exchange_symbol: str) -> float:
        """Async helper for getting current price."""
        async with aiohttp.ClientSession() as session:
            return await self._get_price_with_subscription("bybit", exchange_symbol, session)

    def _fetch_collateral_tiers(self, standardized_symbol: str = None):
        """Fetch collateral tier information from Bybit API using direct endpoint"""
        if standardized_symbol:
            base_coin = standardized_symbol.split("-")[0]
            
            # Check cache first
            if not hasattr(self, '_collateral_cache'):
                self._collateral_cache = {}
                
            if base_coin in self._collateral_cache:
                cached_data = self._collateral_cache[base_coin]
                if time.time() - cached_data['timestamp'] < 3600:  # 1 hour cache
                    return cached_data['data']
            
            # Cache miss or expired - fetch fresh data
            url = "https://api.bybit.com/v5/spot-margin-trade/collateral"
            params = {"currency": base_coin}
            try:
                response = requests.get(url, params=params, timeout=10)
                data = response.json()
                
                if data.get("retCode") != 0:
                    logger.error(f"API error fetching collateral for {base_coin}: {data.get('retMsg')}")
                    return {}
                    
                result_list = data.get("result", {}).get("list", [])
                if not result_list:
                    logger.warning(f"No collateral tiers found for {base_coin} - not accepted as collateral")
                    # Cache empty result to prevent repeated API calls
                    self._collateral_cache[base_coin] = {
                        "data": {},
                        "timestamp": time.time()
                    }
                    return {}
                
                collateral_data = result_list[0]
                # Update cache with valid data
                self._collateral_cache[base_coin] = {
                    "data": collateral_data,
                    "timestamp": time.time()
                }
                
                return collateral_data
                
            except Exception as e:
                logger.error(f"Error fetching collateral info for {base_coin}: {e}")
                return {}
    
    def get_eligible_collateral_qty(self, standardized_symbol: str, usd_value: float, price: float = None) -> float:
        """
        Calculate theoretical collateral value for Bybit.

        Args:
            standardized_symbol (str): The standardized symbol (e.g., 'BTC-USDT').
            usd_value (float): The USD value of the coin quantity.
            price (float, optional): The price of the coin in USD. If provided and > 0,
                                     this price will be used instead of fetching it.

        Returns:
            float: The eligible collateral quantity in USD.
        """
        # Handle stablecoins directly
        if standardized_symbol.split('-')[0] in ['USDT', 'USDC']:
             return usd_value

        # Fetch collateral info using standardized symbol (extracts base coin internally)
        collateral_info = self._fetch_collateral_tiers(standardized_symbol)

        if not collateral_info or 'collateralRatioList' not in collateral_info:
            logger.warning(f"No collateral info found for {standardized_symbol}. Not eligible as collateral.")
            return 0.0  # Not eligible as collateral
        
        tier_list = collateral_info.get('collateralRatioList', [])

        if not tier_list:
             logger.warning(f"No collateral tiers found for {standardized_symbol}. Not eligible as collateral.")
             return 0.0

        # Sort tiers by minQty
        tier_list.sort(key=lambda x: float(x.get('minQty', 0)))

        # Get current price to calculate quantity - use provided price if available and valid
        if price is None or price <= 0:
            # Need the exchange spot symbol to fetch the price
            exchange_spot_symbol = self.convert_to_spot_exchange_symbol(standardized_symbol)
            if not exchange_spot_symbol:
                 logger.error(f"Could not find exchange spot symbol for {standardized_symbol} to fetch price for collateral calculation.")
                 return 0.0
            price = self._get_current_price(exchange_spot_symbol)

            if not price or price <= 0:
                logger.error(f"Could not get a valid price for {standardized_symbol} to calculate collateral.")
                return 0.0 # Cannot calculate collateral without a valid price

        # Calculate the total quantity of the coin
        coin_quantity = usd_value / price if price > 0 else 0.0

        collateral_quantity_in_coin = 0.0
        remaining_quantity = coin_quantity

        # Iterate through tiers and calculate collateral based on quantity
        for tier in tier_list:
            min_qty = float(tier.get('minQty', 0))
            max_qty_str = tier.get('maxQty', '')
            collateral_ratio = float(tier.get('collateralRatio', 0))

            if remaining_quantity <= 0:
                break # No more quantity to allocate to tiers

            tier_range_qty = 0.0
            if max_qty_str == "":
                # This is the last tier, it covers all remaining quantity above min_qty
                tier_range_qty = max(0, remaining_quantity - min_qty)
            else:
                max_qty = float(max_qty_str)
                if coin_quantity > max_qty:
                    # The total quantity exceeds this tier, take the full tier range
                    tier_range_qty = max(0, max_qty - min_qty)
                else:
                    # The total quantity falls within or below this tier, take the quantity from min_qty up to the total quantity
                    tier_range_qty = max(0, remaining_quantity - min_qty)

            collateral_quantity_in_coin += tier_range_qty * collateral_ratio
            remaining_quantity -= tier_range_qty # Subtract the quantity allocated to this tier

        # Convert the total collateral quantity back to USD
        collateral_value_usd = collateral_quantity_in_coin * price

        # Note: Bybit's API response for risk parameters doesn't seem to include a collateral cap per coin
        # like Crypto.com's base_currency_config. If a cap exists, it would need to be handled here.
        # Based on the provided collateral_info structure, there is no cap field.

        return collateral_value_usd

    def get_standardized_unified_data(self, nonzero_only: bool = True) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Get unified account data (balances and positions)"""
        try:
            # Fetch wallet balance (for total equity, locked funds, etc.)
            balance_response = self.client.get_wallet_balance(accountType="unified")
            logger.info(f"Bybit API response (get_wallet_balance): {balance_response}")
            
            if not balance_response:
                logger.error("Empty response for get_wallet_balance from Bybit")
                return pd.DataFrame(), pd.DataFrame()
                
            try:
                result = balance_response['result']
                result_list = result['list']
                if not result_list:
                    logger.error("Empty list in get_wallet_balance response")
                    return pd.DataFrame(), pd.DataFrame()
                account_data = result_list[0]
            except KeyError as e:
                logger.error(f"Invalid response format for get_wallet_balance: {e}")
                return pd.DataFrame(), pd.DataFrame()

            # Fetch positions
            positions_response = self.client.get_positions(category="linear", settleCoin="USDT")
            logger.info(f"Bybit API response (get_positions): {positions_response}")

            positions_data = []
            try:
                if positions_response:
                    result = positions_response['result']
                    positions_data = result['list']
            except KeyError as e:
                logger.warning(f"Could not extract positions data: {e}")

            # Create a dictionary to map symbols to their position data
            positions_map = {}
            for pos in positions_data:
                try:
                    symbol = pos['symbol']
                    mark_price = float(pos['markPrice']) if 'markPrice' in pos else 0
                    avg_price = float(pos['avgPrice']) if 'avgPrice' in pos else 0
                    size = float(pos['size']) if 'size' in pos else 0
                    side_multiplier = -1 if pos.get('side') == 'Sell' else 1

                    try:
                        unrealized_pnl = float(pos['unrealisedPnl'])
                    except (KeyError, ValueError):
                        unrealized_pnl = 0.0

                    positions_map[symbol] = {
                        'mark_price': mark_price,
                        'entry_price': avg_price,
                        'size': size * side_multiplier,
                        'unrealized_pnl': unrealized_pnl
                    }
                except KeyError as e:
                    logger.warning(f"Missing required field in position data: {e}")
                    continue
                except ValueError as e:
                    logger.warning(f"Invalid numeric value in position data: {e}")
                    continue

            # Summary DataFrame (total balance, locked, etc.)
            try:
                total_equity = float(account_data['totalEquity'])
                total_margin_balance = float(account_data['totalMarginBalance'])
                total_initial_margin = float(account_data['totalInitialMargin'])
                total_available_balance = float(account_data['totalAvailableBalance'])
                total_perp_upl = float(account_data['totalPerpUPL'])
                total_wallet_balance = float(account_data['totalWalletBalance'])
                total_maintenance_margin = float(account_data['totalMaintenanceMargin'])
            except KeyError as e:
                logger.error(f"Missing required account data field: {e}")
                total_equity = 0.0
                total_margin_balance = 0.0
                total_initial_margin = 0.0
                total_available_balance = 0.0
                total_perp_upl = 0.0
                total_wallet_balance = 0.0
                total_maintenance_margin = 0.0

            summary_df = pd.DataFrame([{
                'exchange': 'bybit',
                'total_balance': total_equity,
                'total_margin_balance': total_margin_balance,
                'total_initial_margin': total_initial_margin,
                'total_available_balance': total_available_balance,
                'total_unrealized_pnl': total_perp_upl,
                'total_wallet_balance': total_wallet_balance,
                'total_maintenance_margin': total_maintenance_margin,
                'total_positions': len(positions_data)
            }])

            # Assets DataFrame (combine balance and position info)
            assets = []

            try:
                coin_list = account_data['coin']
            except KeyError:
                logger.error("No 'coin' field found in account data")
                coin_list = []

            for coin_data in coin_list:
                try:
                    symbol = coin_data['coin']

                    try:
                        wallet_balance = float(coin_data['walletBalance'])
                    except (KeyError, ValueError):
                        logger.warning(f"Could not get wallet balance for {symbol}, setting to 0")
                        wallet_balance = 0.0

                    try:
                        locked = float(coin_data['locked'])
                    except (KeyError, ValueError):
                        logger.warning(f"Could not get locked amount for {symbol}, setting to 0")
                        locked = 0.0

                    if nonzero_only and wallet_balance == 0 and symbol + "USDT" not in positions_map:
                        continue  # Skip if no balance and no position

                    # For stablecoins, use a fixed price of 1.0
                    if symbol in ['USDT', 'USDC']:
                        mark_price = 1.0
                        entry_price = 1.0
                        position_size = 0.0  # No position for stablecoins
                        position_usd = 0.0  # No position USD value for stablecoins
                        balance_usd = wallet_balance  # For stablecoins, balance USD = wallet balance

                        assets.append({
                            'symbol': symbol,
                            'walletBalance': wallet_balance,
                            'locked': locked,
                            'position_usd': position_usd,    # USD value of the perpetual position
                            'balance_usd': balance_usd,      # USD value of the spot balance
                            'total_position_size': position_size,
                            'unrealized_pnl': 0,  # No PnL for stablecoins
                            'mark_price': mark_price,
                            'entry_price': entry_price
                        })
                    else:
                        # Check if there's a corresponding position (perp futures)
                        perp_symbol = symbol + "USDT"

                        if perp_symbol in positions_map:
                            # We have both a balance (spot) and a position (futures)
                            position_info = positions_map[perp_symbol]
                            mark_price = position_info['mark_price']
                            entry_price = position_info['entry_price']
                            position_size = position_info['size']
                            unrealized_pnl = position_info['unrealized_pnl']
                            position_usd = position_size * mark_price if mark_price > 0 else 0

                            # Get USD value of the spot balance
                            try:
                                balance_usd = float(coin_data['usdValue']) if 'usdValue' in coin_data else 0
                            except (KeyError, ValueError):
                                balance_usd = 0
                        else:
                            # We only have a balance (spot), no position (futures)
                            try:
                                # Try to get USD value from coin data
                                balance_usd = float(coin_data['usdValue']) if 'usdValue' in coin_data else 0
                                mark_price = balance_usd / wallet_balance if wallet_balance > 0 else 0
                            except (KeyError, ValueError):
                                mark_price = 0
                                balance_usd = 0

                            entry_price = 0  # No entry price for spot-only
                            position_size = 0  # No position size for spot-only
                            unrealized_pnl = 0  # No unrealized PnL for spot-only
                            position_usd = 0  # No position USD value for spot-only

                        assets.append({
                            'symbol': symbol,
                            'walletBalance': wallet_balance,
                            'locked': locked,
                            'position_usd': position_usd,    # USD value of the perpetual position
                            'balance_usd': balance_usd,      # USD value of the spot balance
                            'total_position_size': position_size,
                            'unrealized_pnl': unrealized_pnl,
                            'mark_price': mark_price,
                            'entry_price': entry_price
                        })
                except KeyError as e:
                    logger.warning(f"Missing required field in coin data: {e}")
                    continue

            assets_df = pd.DataFrame(assets) if assets else pd.DataFrame(
                columns=['symbol', 'walletBalance', 'locked', 'position_usd', 'balance_usd', 'total_position_size', 'unrealized_pnl', 'mark_price', 'entry_price']
            )

            logger.info(f"Bybit assets list: {assets}")
            return summary_df, assets_df

        except (InvalidRequestError, FailedRequestError) as e:
            if "Your api key has expired" in str(e) or "ErrCode: 401" in str(e):
                current_time = time.time()
                error_key = "bybit_api_key_invalid"
                if should_log_error(error_key, current_time):
                    logger.warning(f"Bybit API key is invalid or expired. Skipping Bybit data retrieval. Error: {str(e)}")
            else:
                logger.error(f"Request failed when getting Bybit unified data: {str(e)}", exc_info=True)
            return pd.DataFrame(), pd.DataFrame()
        except Exception as e:
            logger.error(f"Error getting Bybit unified data: {str(e)}", exc_info=True)
            return pd.DataFrame(), pd.DataFrame()
    
    def get_balances(self, nonzero_only: bool = True) -> List[Balance]:
        """Get account balances"""
        try:
            response = self.client.get_wallet_balance(accountType="unified")
            if not response or 'result' not in response:
                logger.error("Invalid response format from Bybit")
                return []
                
            balances = []
            for coin in response['result']['list'][0]['coin']:
                total = float(coin.get('walletBalance', 0))
                if nonzero_only and total == 0:
                    continue
                withdraw_qty= coin.get('availableToWithdraw', 0)
                if withdraw_qty == '':
                    withdraw_qty = 0
                    
                balances.append(Balance(
                    symbol=coin.get('coin', ''),
                    total=total,
                    available=float(withdraw_qty),
                    in_order=float(coin.get('locked', 0))
                ))
                
            return balances
            
        except Exception as e:
            logger.error(f"Error getting Bybit balances: {str(e)}", exc_info=True)
            return []
    
    def get_positions(self, nonzero_only: bool = True) -> List[Position]:
        """Get open positions"""
        try:
            positions = []
            cursor = ""
            while True:
                response = self.client.get_positions(
                    category="linear",
                    settleCoin="USDT",
                    limit=200,
                    cursor=cursor
                )
                
                if not response or response.get('retCode') != 0:
                    break
                    
                positions_data = response['result']['list']
                if not positions_data:
                    break
                    
                cursor = response['result'].get('nextPageCursor', '')
                
                for pos in positions_data:
                    size = float(pos.get('size', 0))
                    if pos.get('side') == 'Sell':
                        size = -size
                        
                    if nonzero_only and size == 0:
                        continue
                        
                    # Convert exchange symbol to standardized format for the position
                    exchange_symbol = pos.get('symbol', '')
                    standardized_symbol = self.convert_to_standardized_symbol(exchange_symbol)
                    
                    positions.append(Position(
                        symbol=standardized_symbol,  # Use standardized symbol format
                        size=size,
                        entry_price=float(pos.get('avgPrice', 0)),
                        mark_price=float(pos.get('markPrice', 0)),
                        unrealized_pnl=float(pos.get('unrealisedPnl', 0))
                    ))
                    
                if not cursor:
                    break
        
            return positions
            
        except Exception as e:
            logger.error(f"Error getting Bybit positions: {str(e)}", exc_info=True)
            return []
            
    def get_mark_prices(self, standardized_symbols: List[str]) -> Dict[str, float]:
        """Get mark prices for given standardized symbols"""
        try:
            # Convert standardized symbols to exchange format
            # Group symbols by category (spot vs linear)
            spot_symbols = []
            linear_symbols = []
            exchange_symbols = []
            for symbol in standardized_symbols:
                if ".PERP" in symbol:
                    exchange_symbol = self.convert_to_perp_exchange_symbol(symbol)
                    linear_symbols.append(exchange_symbol)
                else:
                    exchange_symbol = self.convert_to_spot_exchange_symbol(symbol)
                    spot_symbols.append(exchange_symbol)
                if exchange_symbol:
                    exchange_symbols.append(exchange_symbol)
            
            mark_prices = {}
            
            # Get spot prices
            if spot_symbols:
                spot_response = self.client.get_tickers(category="spot", symbol=",".join(spot_symbols))
                if spot_response and spot_response.get('retCode') == 0:
                    for ticker in spot_response['result']['list']:
                        exchange_symbol = ticker.get('symbol', '')
                        last_price = float(ticker.get('lastPrice', 0))
                        standardized_symbol = self.convert_to_spot_standardized_symbol(exchange_symbol)
                        mark_prices[standardized_symbol] = last_price
            
            # Get linear perpetual prices
            if linear_symbols:
                linear_response = self.client.get_tickers(category="linear", symbol=",".join(linear_symbols))
                if linear_response and linear_response.get('retCode') == 0:
                    for ticker in linear_response['result']['list']:
                        exchange_symbol = ticker.get('symbol', '')
                        mark_price = float(ticker.get('markPrice', 0))
                        standardized_symbol = self.convert_to_perp_standardized_symbol(exchange_symbol)
                        mark_prices[standardized_symbol] = mark_price
            
            return mark_prices
            
        except Exception as e:
            logger.error(f"Error getting Bybit mark prices: {str(e)}", exc_info=True)
            return {}
            
    def get_tickers(self, category: str) -> Dict:
        """Get tickers for a specific category, standardized as dict keyed by symbol"""
        try:
            response = self.client.get_tickers(category=category)
            tickers_dict = {}
            if response and response.get('retCode') == 0:
                ticker_list = response.get('result', {}).get('list', [])
                for ticker in ticker_list:
                    symbol = ticker.get('symbol')
                    if symbol:
                        tickers_dict[symbol] = ticker
            else:
                logger.warning(f"Bybit get_tickers for category {category} failed or returned error: {response}")
            return tickers_dict
        except Exception as e:
            logger.error(f"Error getting Bybit tickers for category {category}: {e}")
            return {}
            
    def get_collateral_info(self, standardized_symbol: str = None) -> List[Dict]:
        """
        Fetches collateral parameters from Bybit API.
        These parameters change rarely and can be cached.
        """
        try:
            # This endpoint returns collateral tier information
            response = self.client.get_collateral_info()
            if not response or response.get('retCode') != 0:
                logger.error(f"Invalid response format or empty response from Bybit API: {response}")
                return []
            return response
        except Exception as e:
            logger.error(f"Error getting Bybit collateral info: {e}")
            return []
            
    # def _refresh_collateral_cache(self):
    #     """Refresh the collateral tier cache"""
    #     try:
    #         # Use the correct endpoint
    #         params = {}
    #         response = self.client.get_collateral_info()
            
    #         if not response or response.get('retCode') != 0:
    #             logger.error(f"Error fetching collateral info: {response}")
    #             return
                
    #         collateral_list = response.get('result', {}).get('list', [])
            
    #         # Update cache
    #         self._collateral_cache = {}
    #         for item in collateral_list:
    #             currency = item.get('currency')
    #             if currency:
    #                 self._collateral_cache[currency] = item
                    
    #         self._collateral_cache_timestamp = time.time()
    #         logger.info(f"Refreshed collateral cache with {len(self._collateral_cache)} items")
                
    #     except Exception as e:
    #         logger.error(f"Error refreshing collateral cache: {e}")

try:
    bybit_client = HTTP(
        testnet=False,
        api_key=os.getenv('BYBIT_QD_READ_API_KEY'),
        api_secret=os.getenv('BYBIT_QD_READ_API_SECRET')
    )
except Exception as e:
    logger.error(f"Failed to initialize Bybit client: {e}")
    bybit_client = None
