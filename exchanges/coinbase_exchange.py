import os
import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import time
import asyncio
import aiohttp
from decimal import Decimal
import json

# Coinbase SDK imports with graceful fallback
try:
    from coinbase.rest import RESTClient
    from requests.exceptions import HTTPError
    from coinbase.rest.types.accounts_types import Account
    from coinbase.rest.types.product_types import Product
    from coinbase.rest.types.futures_types import FCMPosition
    from coinbase.rest.types.portfolios_types import PortfolioBreakdown, PortfolioPosition
    COINBASE_AVAILABLE = True
except ImportError as e:
    logger = logging.getLogger(__name__)

    # Detect environment for better error messages
    is_azure = bool(os.getenv('WEBSITE_SITE_NAME'))
    is_docker = os.path.exists('/.dockerenv')

    if is_azure:
        logger.error(f"🔧 AZURE SETUP REQUIRED: Coinbase SDK not available in Azure container: {e}")
        logger.error("💡 Add to your Dockerfile: RUN pip install coinbase-advanced-py")
        logger.error("📖 See AZURE_SETUP_GUIDE.md for complete setup instructions")
    elif is_docker:
        logger.error(f"🔧 DOCKER SETUP REQUIRED: Coinbase SDK not available in Docker container: {e}")
        logger.error("💡 Add to your Dockerfile: RUN pip install coinbase-advanced-py")
    else:
        logger.warning(f"Coinbase SDK not available: {e}")
        logger.warning("💡 Install with: pip install coinbase-advanced-py")

    # Create dummy classes to prevent import errors
    RESTClient = None
    HTTPError = Exception
    Account = dict
    Product = dict
    FCMPosition = dict
    PortfolioBreakdown = dict
    PortfolioPosition = dict
    COINBASE_AVAILABLE = False

try:
    from .exchange_interface import ExchangeInterface, Balance, Position
except ImportError:
    # This is for standalone execution/testing
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from exchanges.exchange_interface import ExchangeInterface, Balance, Position

logger = logging.getLogger(__name__)

# <ai_context>
# This file implements the Coinbase exchange integration, adhering to the `ExchangeInterface`.
# It uses the `coinbase-advanced-py` SDK for interacting with the Coinbase Advanced Trade API.
# Key features include:
# - Fetching and standardizing instrument information for spot and futures.
# - Retrieving account balances and futures positions specific to a portfolio.
# - Calculating eligible collateral based on Coinbase's haircut rates.
# - Providing unified account data (summary and asset breakdown).
# - Fetching mark prices and tickers.
# - A wizard in the `if __name__ == "__main__":` block to help users select a portfolio UUID
#   and verify certain data fields from their live account.
#
# Assumptions:
# - API key and secret are provided via environment variables `COINBASE_API_KEY` and `COINBASE_API_SECRET`.
# - A `portfolio_uuid` is provided during class instantiation, which is used for most portfolio-specific calls.
# - The internal pricing service (`_get_current_price`) is available at `http://localhost:8003`.
# - Collateral haircut rates are fetched from the portfolio breakdown and cached.
# </ai_context>
class CoinbaseExchange(ExchangeInterface):
    def __init__(self, portfolio_uuid: str):
        if not COINBASE_AVAILABLE:
            # Detect environment for better error messages
            is_azure = bool(os.getenv('WEBSITE_SITE_NAME'))
            is_docker = os.path.exists('/.dockerenv')

            if is_azure:
                raise ImportError(
                    "🔧 AZURE SETUP REQUIRED: Coinbase SDK not available in Azure container.\n"
                    "💡 Add to your Dockerfile: RUN pip install coinbase-advanced-py\n"
                    "📖 See AZURE_SETUP_GUIDE.md for complete setup instructions"
                )
            elif is_docker:
                raise ImportError(
                    "🔧 DOCKER SETUP REQUIRED: Coinbase SDK not available in Docker container.\n"
                    "💡 Add to your Dockerfile: RUN pip install coinbase-advanced-py"
                )
            else:
                raise ImportError(
                    "Coinbase SDK is not available.\n"
                    "💡 Install with: pip install coinbase-advanced-py"
                )

        json_file_path = 'coinbasekeys.json'
        try:
            with open(json_file_path, 'r') as f:
                credentials = json.load(f)
            self.api_key = credentials.get("name")
            self.api_secret = credentials.get("privateKey")
            # Ensure the private key has correct newline characters if stored with escaped newlines
            if self.api_secret and '\\n' in self.api_secret:
                 self.api_secret = self.api_secret.replace('\\n', '\n')
        except (FileNotFoundError, json.JSONDecodeError, AttributeError) as e:
            msg = f"Error reading Coinbase API credentials from {json_file_path}: {e}"
            logger.error(msg)
            raise ValueError(msg)

        self.portfolio_uuid = portfolio_uuid

        if not self.api_key or not self.api_secret:
            msg = f"Coinbase API credentials ('name' or 'privateKey') not found in {json_file_path}."
            logger.error(msg)
            raise ValueError(msg)
        
        if not self.portfolio_uuid:
            msg = "CoinbaseExchange requires a portfolio_uuid."
            logger.error(msg)
            raise ValueError(msg)

        try:
            self.client = RESTClient(api_key=self.api_key, api_secret=self.api_secret)
            # Test connection by fetching portfolios (accessible without specific portfolio_uuid context for list)
            # self.client.get_portfolios() # Defer any network calls from constructor
        except Exception as e:
            logger.error(f"Unexpected error during Coinbase client initialization: {e}", exc_info=True)
            raise

        self.collateral_pool_name = "coinbase"
        self._collateral_haircut_cache: Dict[str, float] = {}
        self._collateral_haircut_cache_timestamp: float = 0.0
        
        self.instrument_standardized_info: List[Dict[str, Any]] = []

    def supports_funding_rates(self) -> bool:
        """Coinbase does not support funding rates (no perpetual futures)"""
        return False

    async def initialize_standardized_instrument_info(self):
        """Initialize standardized instrument information."""
        try:
            # Test connection by fetching portfolios before getting instrument info
            loop = asyncio.get_running_loop()
            await loop.run_in_executor(None, self.client.get_portfolios)
            
            self.instrument_standardized_info = await self.get_standardized_instrument_info()
            logger.info(f"Successfully initialized instrument info for {self.collateral_pool_name}.")
        except HTTPError as e:
            logger.error(f"Coinbase API Error during client initialization/connection test: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Coinbase instrument info: {e}", exc_info=True)
            raise
        
    async def update_standardized_instrument_info(self):
        """Update the standardized instrument information if needed."""
        self.instrument_standardized_info = await self.get_standardized_instrument_info()

    def _str_to_float(self, value: Optional[str], default: float = 0.0) -> float:
        if value is None or value == '':
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"Could not convert '{value}' to float, using default {default}")
            return default

    async def get_standardized_instrument_info(self) -> List[Dict[str, Any]]:
        """Fetch and standardize instrument information from Coinbase."""
        standardized_instruments = []
        try:
            loop = asyncio.get_running_loop()
            # Using get_all_products which should handle pagination
            products_response = await loop.run_in_executor(None, self.client.get_products)
            
            if not products_response or not products_response.products:
                logger.error("No products received from Coinbase API.")
                return []

            for i, prod in enumerate(products_response.products):
                # if i == 0: # Print info for the first product
                #     print(f"Type of product object: {type(prod)}")
                #     print(f"Dir of product object: {dir(prod)}")
                is_spot = prod.product_type == "SPOT"
                is_future = prod.product_type == "FUTURE"
                is_perpetual = False
                is_dated_future = False

                unique_fields = {
                    'product_type': prod.product_type,
                    'status': prod.status,
                    'is_disabled': prod.is_disabled,
                    'trading_disabled': prod.trading_disabled,
                    'cancel_only': prod.cancel_only,
                    'limit_only': prod.limit_only,
                    'post_only': prod.post_only,
                    'base_display_symbol': prod.base_display_symbol,
                    'quote_display_symbol': prod.quote_display_symbol,
                    'view_only': prod.view_only,
                    'price_increment': self._str_to_float(prod.price_increment),
                    'base_min_size': self._str_to_float(prod.base_min_size),
                    'base_max_size': self._str_to_float(prod.base_max_size),
                    # 'fcm_product_id': prod.fcm_product_id, # Attribute not found in this version
                    'trading_allowed': not prod.is_disabled and not prod.trading_disabled and prod.status == 'ACTIVE', # Added based on available attributes
                    'auction_mode': prod.auction_mode,
                    'alias_to': prod.alias_to,
                    'base_name': prod.base_name,
                    'quote_name': prod.quote_name,
                    'leverage': None, # Default for spot
                    'contract_size': None, # Default for spot
                    'contract_value_currency': None,
                    'contract_expiry_type': None,
                    'expected_settlement_time': None,
                    'underlying_asset': None,
                    'maintenance_margin_rate': None,
                    'initial_margin_rate': None,
                    'position_limit_size': None,
                }

                if is_future and prod.futures_product_details:
                    fpd = prod.futures_product_details
                    
                    # Log the actual futures product details structure
                    logger.info(f"Futures product details for {prod.product_id}:")
                    logger.info(f"  contract_expiry_type: {fpd.contract_expiry_type}")
                    logger.info(f"  Available attributes: {[attr for attr in dir(fpd) if not attr.startswith('_')]}")
                    
                    is_perpetual = fpd.contract_expiry_type == "PERPETUAL"
                    is_dated_future = fpd.contract_expiry_type != "PERPETUAL"
                    
                    # Log what we're actually seeing
                    logger.info(f"  is_perpetual: {is_perpetual}, is_dated_future: {is_dated_future}")
                    
                    unique_fields.update({
                        'leverage': self._str_to_float(fpd.leverage),
                        'contract_size': self._str_to_float(fpd.contract_size),
                        'contract_value_currency': fpd.venue_native_asset,
                        'contract_expiry_type': fpd.contract_expiry_type,
                        'expected_settlement_time': fpd.expected_settlement_time.isoformat() if fpd.expected_settlement_time else None,
                        'underlying_asset': fpd.underlying_asset,
                        'maintenance_margin_rate': self._str_to_float(fpd.maintenance_margin_rate),
                        'initial_margin_rate': self._str_to_float(fpd.initial_margin_rate),
                        'position_limit_size': self._str_to_float(fpd.position_limit_size),
                        'contract_display_name': fpd.contract_display_name,
                        'max_order_size_in_base': self._str_to_float(fpd.max_order_size_in_base),
                        'max_order_size_in_contract': self._str_to_float(fpd.max_order_size_in_contract),
                        'min_order_size_in_base': self._str_to_float(fpd.min_order_size_in_base),
                        'min_order_size_in_contract': self._str_to_float(fpd.min_order_size_in_contract),
                        'tick_size': self._str_to_float(fpd.tick_size),
                        'price': self._str_to_float(prod.price),
                        'mid_market_price': self._str_to_float(prod.mid_market_price),
                        'volume_24h': self._str_to_float(prod.volume_24h),
                        'open_interest': self._str_to_float(fpd.open_interest),
                        'current_funding_rate': self._str_to_float(fpd.current_funding_rate),
                        'next_funding_time': fpd.next_funding_time.isoformat() if fpd.next_funding_time else None,
                    })
                    
                    # Log what venue_native_asset actually contains
                    logger.info(f"  venue_native_asset (used for contract_value_currency): {fpd.venue_native_asset}")
                
                # For base_precision and quote_precision, base_increment and quote_increment are key
                # For tick_size, quote_increment is key for SPOT. For Futures, it's in futures_product_details
                base_precision = self._str_to_float(prod.base_increment)
                tick_size = self._str_to_float(prod.quote_increment) if is_spot else unique_fields.get('tick_size', 0.0)
                
                # Quote precision is essentially the tick_size for quote currency representation
                quote_precision = tick_size 

                instrument_info = {
                    'exchange': 'coinbase',
                    'is_spot': is_spot,
                    'is_perpetual': is_perpetual,
                    'is_dated_future': is_dated_future,
                    'exchange_format_symbol': prod.product_id,
                    'base_coin': prod.base_currency_id,
                    'quote_coin': prod.quote_currency_id,
                    'standardized_format_symbol': f"{prod.base_currency_id}-{prod.quote_currency_id}",
                    'standardized_format_perp': f"{prod.base_currency_id}-{prod.quote_currency_id}.PERP",
                    'base_precision': base_precision,
                    'quote_precision': quote_precision,
                    'tick_size': tick_size,
                    'exchange_unique_field_list': unique_fields,
                }
                standardized_instruments.append(instrument_info)
            
            logger.info(f"Fetched and standardized {len(standardized_instruments)} instruments from Coinbase.")

        except HTTPError as e:
            logger.error(f"Coinbase API error fetching instrument info: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Unexpected error fetching Coinbase instrument info: {e}", exc_info=True)
        
        return standardized_instruments

    def convert_to_spot_standardized_symbol(self, exchange_symbol: str) -> str:
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_spot'):
                return instrument.get('standardized_format_symbol', '')
        logger.warning(f"No spot symbol found for Coinbase exchange symbol {exchange_symbol}")
        return ""

    def convert_to_perp_standardized_symbol(self, exchange_symbol: str, skip_exchange_specific_stable_conversion: bool = False) -> str:
        for instrument in self.instrument_standardized_info:
            if instrument.get('exchange_format_symbol') == exchange_symbol and instrument.get('is_perpetual'):
                return instrument.get('standardized_format_perp', '')
        logger.warning(f"No perp symbol found for Coinbase exchange symbol {exchange_symbol}")
        return ""

    def convert_to_spot_exchange_symbol(self, standardized_symbol: str) -> str:
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_symbol') == standardized_symbol and instrument.get('is_spot'):
                return instrument.get('exchange_format_symbol', '')
        logger.warning(f"No Coinbase exchange symbol found for spot standardized symbol {standardized_symbol}")
        return ""

    def convert_to_perp_exchange_symbol(self, standardized_symbol: str, skip_exchange_specific_stable_conversion: bool = False) -> str:
        for instrument in self.instrument_standardized_info:
            if instrument.get('standardized_format_perp') == standardized_symbol and instrument.get('is_perpetual'):
                return instrument.get('exchange_format_symbol', '')
        logger.warning(f"No Coinbase exchange symbol found for perp standardized symbol {standardized_symbol}")
        return ""

    def _fetch_collateral_haircut_rates(self) -> Dict[str, float]:
        """Fetch and cache collateral haircut rates per asset from portfolio breakdown."""
        current_time = time.time()
        # Cache for 6 hours (21600 seconds)
        if self._collateral_haircut_cache and (current_time - self._collateral_haircut_cache_timestamp < 21600):
            return self._collateral_haircut_cache

        try:
            logger.info(f"Fetching portfolio breakdown for portfolio UUID: {self.portfolio_uuid} to update haircut rates.")
            breakdown = self.client.get_portfolio_breakdown(portfolio_uuid=self.portfolio_uuid)
            
            new_cache: Dict[str, float] = {}
            # Access assets from the nested breakdown object
            nested_breakdown = breakdown.breakdown if breakdown and hasattr(breakdown, 'breakdown') else None
            if nested_breakdown and hasattr(nested_breakdown, 'spot_positions') and nested_breakdown.spot_positions: # Assuming spot positions contain assets with haircut rates
                for asset_info in nested_breakdown.spot_positions:
                    # Assuming asset_info is a PortfolioPosition object
                    asset_id = asset_info.asset_id if hasattr(asset_info, 'asset_id') else None
                    haircut_rate_str = asset_info.collateral_haircut_rate.value if hasattr(asset_info, 'collateral_haircut_rate') and hasattr(asset_info.collateral_haircut_rate, 'value') else "1.0" # Default to 100% haircut if not specified
                    new_cache[asset_id] = self._str_to_float(haircut_rate_str, 1.0)
            
            self._collateral_haircut_cache = new_cache
            self._collateral_haircut_cache_timestamp = current_time
            logger.info(f"Updated Coinbase collateral haircut cache with {len(new_cache)} assets.")
            return self._collateral_haircut_cache
        except HTTPError as e:
            logger.error(f"Coinbase API error fetching portfolio breakdown for haircut rates: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Unexpected error fetching portfolio breakdown for haircut rates: {e}", exc_info=True)
        
        # Return old cache or empty if fetch failed, to prevent breaking operations
        return self._collateral_haircut_cache if self._collateral_haircut_cache else {}


    def get_eligible_collateral_qty(self, standardized_symbol: str, usd_value: float, price: float = None) -> float:
        """Calculate eligible collateral quantity in USD for a given standardized symbol and its USD value."""
        if not standardized_symbol:
            logger.warning("Standardized symbol is empty for get_eligible_collateral_qty.")
            return 0.0
        
        parts = standardized_symbol.split('-')
        base_coin = parts[0]

        # Stablecoins that might have 0 haircut by default (e.g., USD itself)
        # However, rely on API for actual haircut rates.
        if base_coin in ["USD", "USDC"] and base_coin not in self._fetch_collateral_haircut_rates(): # Check if API gives specific rate
             logger.info(f"Assuming 0% haircut for stablecoin {base_coin} if not specified in API.")
             # This is a strong assumption, API-provided haircut is king.
             # If API provides a haircut for USDC, it will be used.
             # If USD is the quote currency it implies 0 haircut essentially.

        haircut_rates = self._fetch_collateral_haircut_rates()
        haircut_rate = haircut_rates.get(base_coin)

        if haircut_rate is None:
            logger.warning(f"No haircut rate found for {base_coin} in Coinbase collateral cache. Assuming 0 collateral value.")
            return 0.0
        
        eligible_value = usd_value * (1.0 - haircut_rate)
        return max(0.0, eligible_value) # Ensure non-negative


    def get_balances(self, nonzero_only: bool = True) -> List[Balance]:
        """Get account balances for the specified portfolio."""
        balances: List[Balance] = []
        try:
            # The SDK's list_accounts method accepts **kwargs, which are passed as params.
            # Portfolio_uuid should work here.
            accounts_response = self.client.get_accounts(limit=250, portfolio_uuid=self.portfolio_uuid)
            
            if accounts_response and accounts_response.accounts:
                for i, acc in enumerate(accounts_response.accounts):
                    if i == 0: # Print info for the first account
                        print(f"Type of account object: {type(acc)}")
                        print(f"Dir of account object: {dir(acc)}")
                    # Calculate total balance from available and hold if needed, or use available
                    # Based on dir(acc), 'available_balance' and 'hold' are available.
                    # Calculate total balance from available and hold
                    available = self._str_to_float(acc.available_balance.value) if hasattr(acc, 'available_balance') and hasattr(acc.available_balance, 'value') else 0.0
                    in_order = self._str_to_float(acc.hold.value) if hasattr(acc, 'hold') and hasattr(acc.hold, 'value') else 0.0
                    total = available + in_order

                    if nonzero_only and total == 0.0:
                        continue

                    # available and in_order are already extracted above
                    
                    balances.append(Balance(
                        symbol=acc.currency,
                        total=total,
                        available=available,
                        in_order=in_order
                    ))
            else:
                logger.warning("No accounts found or empty response from Coinbase list_accounts.")
        except HTTPError as e:
            logger.error(f"Coinbase API error getting balances: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Unexpected error getting Coinbase balances: {e}", exc_info=True)
        return balances

    def get_positions(self, nonzero_only: bool = True) -> List[Position]:
        """Get open futures positions for the specified portfolio."""
        positions: List[Position] = []
        try:
            futures_positions_response = self.client.list_futures_positions(portfolio_uuid=self.portfolio_uuid)

            if futures_positions_response and futures_positions_response.positions:
                for pos_data in futures_positions_response.positions:
                    pos_data: FCMPosition
                    size = self._str_to_float(pos_data.net_size) # net_size includes side
                    if nonzero_only and size == 0.0:
                        continue

                    standardized_perp_sym = self.convert_to_perp_standardized_symbol(pos_data.product_id)
                    if not standardized_perp_sym:
                        logger.warning(f"Could not convert Coinbase futures product_id {pos_data.product_id} to standardized perp symbol. Skipping position.")
                        continue
                    
                    positions.append(Position(
                        symbol=standardized_perp_sym,
                        size=size,
                        entry_price=self._str_to_float(pos_data.average_entry_price),
                        mark_price=self._str_to_float(pos_data.mark_price),
                        unrealized_pnl=self._str_to_float(pos_data.unrealized_pnl)
                    ))
            else:
                logger.info("No futures positions found or empty response from Coinbase.")
        except HTTPError as e:
            logger.error(f"Coinbase API error getting positions: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Unexpected error getting Coinbase positions: {e}", exc_info=True)
        return positions

    def get_standardized_unified_data(self, nonzero_only: bool = True) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Get standardized unified data containing both balances and positions for the portfolio."""
        summary_df = pd.DataFrame()
        assets_df = pd.DataFrame(columns=['symbol', 'walletBalance', 'locked', 'position_usd', 
                                          'balance_usd', 'total_position_size', 'unrealized_pnl', 
                                          'mark_price', 'entry_price'])
        try:
            breakdown: Optional[PortfolioBreakdown] = self.client.get_portfolio_breakdown(portfolio_uuid=self.portfolio_uuid)
            if not breakdown:
                logger.error("Failed to fetch portfolio breakdown from Coinbase.")
                return summary_df, assets_df

            # Summary Data
            # These fields need to be confirmed via the wizard for correctness
            # Placeholder: User to confirm which field to use for total_unrealized_pnl via wizard.
            # Defaulting to breakdown.unrealized_pnl.current.all_value.
            # Placeholder: User to confirm total_available_balance via wizard.
            # Defaulting to breakdown.total_available_balance_in_usd.value.

            # Access the nested 'breakdown' attribute which contains the actual data
            nested_breakdown = breakdown.breakdown if breakdown and hasattr(breakdown, 'breakdown') else None
            # Add print statements for nested_breakdown object
            # print(f"Type of nested_breakdown object: {type(nested_breakdown)}")
            # print(f"Dir of nested_breakdown object: {dir(nested_breakdown)}")

            # Log the actual summary data structure
            logger.info(f"Summary data structure analysis:")
            if nested_breakdown and hasattr(nested_breakdown, 'portfolio_balances'):
                pb = nested_breakdown.portfolio_balances
                logger.info(f"  portfolio_balances type: {type(pb)}")
                logger.info(f"  portfolio_balances attributes: {[attr for attr in dir(pb) if not attr.startswith('_')]}")
                
                if hasattr(pb, 'total_balance_in_usd'):
                    logger.info(f"  total_balance_in_usd: {pb.total_balance_in_usd}")
                    if hasattr(pb.total_balance_in_usd, 'value'):
                        logger.info(f"  total_balance_in_usd.value: {pb.total_balance_in_usd.value}")

            # Access unrealized_pnl and total_available_balance from nested_breakdown attributes
            # Based on dir(nested_breakdown), attributes like futures_positions, perp_positions, portfolio_balances might contain this data.
            # Using attribute access as PortfolioBreakdown object does not have .get()
            total_unrealized_pnl_val = "0"
            if nested_breakdown and hasattr(nested_breakdown, 'futures_positions') and nested_breakdown.futures_positions and hasattr(nested_breakdown.futures_positions, 'unrealized_pnl') and hasattr(nested_breakdown.futures_positions.unrealized_pnl, 'value'):
                 total_unrealized_pnl_val = nested_breakdown.futures_positions.unrealized_pnl.value
            elif nested_breakdown and hasattr(nested_breakdown, 'perp_positions') and nested_breakdown.perp_positions and hasattr(nested_breakdown.perp_positions, 'unrealized_pnl') and hasattr(nested_breakdown.perp_positions.unrealized_pnl, 'value'):
                 total_unrealized_pnl_val = nested_breakdown.perp_positions.unrealized_pnl.value
            # Add other potential locations for unrealized_pnl if necessary

            total_available_bal_val = "0"
            if nested_breakdown and hasattr(nested_breakdown, 'portfolio_balances') and nested_breakdown.portfolio_balances and hasattr(nested_breakdown.portfolio_balances, 'total_available_balance_in_usd') and hasattr(nested_breakdown.portfolio_balances.total_available_balance_in_usd, 'value'):
                 total_available_bal_val = nested_breakdown.portfolio_balances.total_available_balance_in_usd.value
            elif nested_breakdown and hasattr(nested_breakdown, 'portfolio') and nested_breakdown.portfolio and hasattr(nested_breakdown.portfolio, 'total_available_balance_in_usd') and hasattr(nested_breakdown.portfolio.total_available_balance_in_usd, 'value'):
                 total_available_bal_val = nested_breakdown.portfolio.total_available_balance_in_usd.value
            # Add other potential locations for total_available_balance if necessary
            
            futures_positions = self.get_positions(nonzero_only=False) # Get all for counting
            num_positions = len([p for p in futures_positions if p.size != 0.0])


            summary_data = {
                'exchange': 'coinbase',
                # Access total_balance, total_margin_balance, etc. from nested_breakdown attributes
                # Using attribute access as PortfolioBreakdown object does not have .get()
                'total_balance': self._str_to_float(nested_breakdown.portfolio_balances.total_balance_in_usd.value if nested_breakdown and hasattr(nested_breakdown, 'portfolio_balances') and nested_breakdown.portfolio_balances and hasattr(nested_breakdown.portfolio_balances, 'total_balance_in_usd') and hasattr(nested_breakdown.portfolio_balances.total_balance_in_usd, 'value') else "0"),
                'total_margin_balance': self._str_to_float(nested_breakdown.portfolio_balances.total_balance_in_usd.value if nested_breakdown and hasattr(nested_breakdown, 'portfolio_balances') and nested_breakdown.portfolio_balances and hasattr(nested_breakdown.portfolio_balances, 'total_balance_in_usd') and hasattr(nested_breakdown.portfolio_balances.total_balance_in_usd, 'value') else "0"), # Assuming total_balance_in_usd is total collateralizable value
                'total_initial_margin': self._str_to_float(nested_breakdown.portfolio_balances.total_initial_margin_in_usd.value if nested_breakdown and hasattr(nested_breakdown, 'portfolio_balances') and nested_breakdown.portfolio_balances and hasattr(nested_breakdown.portfolio_balances, 'total_initial_margin_in_usd') and hasattr(nested_breakdown.portfolio_balances.total_initial_margin_in_usd, 'value') else "0"),
                'total_available_balance': self._str_to_float(total_available_bal_val), # To be verified
                'total_unrealized_pnl': self._str_to_float(total_unrealized_pnl_val), # To be verified
                'total_wallet_balance': self._str_to_float(nested_breakdown.portfolio_balances.total_cash_equivalent_value.value if nested_breakdown and hasattr(nested_breakdown, 'portfolio_balances') and nested_breakdown.portfolio_balances and hasattr(nested_breakdown.portfolio_balances, 'total_cash_equivalent_value') and hasattr(nested_breakdown.portfolio_balances.total_cash_equivalent_value, 'value') else "0"),
                'total_maintenance_margin': self._str_to_float(nested_breakdown.portfolio_balances.total_maintenance_margin_in_usd.value if nested_breakdown and hasattr(nested_breakdown, 'portfolio_balances') and nested_breakdown.portfolio_balances and hasattr(nested_breakdown.portfolio_balances, 'total_maintenance_margin_in_usd') and hasattr(nested_breakdown.portfolio_balances.total_maintenance_margin_in_usd, 'value') else "0"),
                'total_positions': num_positions
            }
            summary_df = pd.DataFrame([summary_data])

            # Assets Data
            processed_assets: Dict[str, Dict[str, Any]] = {}

            # Process spot balances from portfolio breakdown assets
            # Access assets list from nested_breakdown attribute
            assets_list = nested_breakdown.spot_positions if nested_breakdown and hasattr(nested_breakdown, 'spot_positions') else [] # Assuming spot balances are in spot_positions
            if assets_list:
                for asset_info in assets_list:
                    # Assuming asset_info is a PortfolioPosition object
                    # The API uses 'asset' instead of 'asset_id'
                    asset_id = asset_info.asset if hasattr(asset_info, 'asset') else None
                    if asset_id is None:
                        logger.warning("Skipping spot asset with None asset in get_standardized_unified_data.")
                        continue
                    # Define base_coin for spot positions
                    base_coin = asset_id # Use asset as base_coin for spot
                    wallet_balance = self._str_to_float(asset_info.total_balance_crypto if hasattr(asset_info, 'total_balance_crypto') else "0")
                    balance_usd_val = self._str_to_float(asset_info.total_balance_fiat if hasattr(asset_info, 'total_balance_fiat') else "0")
                    # Use available_to_trade_crypto as locked balance (total - available = locked)
                    available_crypto = self._str_to_float(asset_info.available_to_trade_crypto if hasattr(asset_info, 'available_to_trade_crypto') else "0")
                    locked_bal = max(0, wallet_balance - available_crypto)

                    mark_price_spot = (balance_usd_val / wallet_balance) if wallet_balance != 0 else 0.0

                    processed_assets[base_coin] = {
                        'symbol': base_coin,
                        'walletBalance': wallet_balance,
                        'locked': locked_bal,
                        'balance_usd': balance_usd_val,
                        'position_usd': 0.0,
                        'total_position_size': 0.0,
                        'unrealized_pnl': 0.0,
                        'mark_price': mark_price_spot, 
                        'entry_price': 0.0
                    }
            
            # Process futures positions
            for pos in futures_positions:
                # Assuming pos.symbol is like "BTC-USD.PERP"
                base_coin = pos.symbol.split('-')[0] # Define base_coin here for futures positions
                
                position_usd_val = pos.size * pos.mark_price

                if base_coin in processed_assets:
                    processed_assets[base_coin].update({
                        'position_usd': position_usd_val,
                        'total_position_size': pos.size,
                        'unrealized_pnl': pos.unrealized_pnl,
                        'mark_price': pos.mark_price, # Futures mark price takes precedence if exists
                        'entry_price': pos.entry_price
                    })
                else:
                    processed_assets[base_coin] = {
                        'symbol': base_coin,
                        'walletBalance': 0.0,
                        'locked': 0.0,
                        'balance_usd': 0.0,
                        'position_usd': position_usd_val,
                        'total_position_size': pos.size,
                        'unrealized_pnl': pos.unrealized_pnl,
                        'mark_price': pos.mark_price,
                        'entry_price': pos.entry_price
                    }

            final_assets_list = list(processed_assets.values())
            if nonzero_only:
                final_assets_list = [
                    asset for asset in final_assets_list
                    if abs(asset['walletBalance']) > 1e-9 or abs(asset['total_position_size']) > 1e-9
                ]
            
            if final_assets_list:
                assets_df = pd.DataFrame(final_assets_list)
            
        except HTTPError as e:
            logger.error(f"Coinbase API error in get_standardized_unified_data: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Unexpected error in Coinbase get_standardized_unified_data: {e}", exc_info=True)
        
        return summary_df, assets_df


    def get_mark_prices(self, standardized_symbols: List[str]) -> Dict[str, float]:
        """Get mark prices for given standardized symbols."""
        mark_prices: Dict[str, float] = {}
        
        spot_product_ids: List[str] = []
        future_product_ids: List[str] = []

        for std_sym in standardized_symbols:
            if ".PERP" in std_sym:
                exchange_sym = self.convert_to_perp_exchange_symbol(std_sym)
                if exchange_sym:
                    future_product_ids.append(exchange_sym)
            else:
                exchange_sym = self.convert_to_spot_exchange_symbol(std_sym)
                if exchange_sym:
                    spot_product_ids.append(exchange_sym)
        
        # Get Spot Mark Prices (mid-price from best bid/ask)
        if spot_product_ids:
            try:
                # Batch request for best bid/ask
                best_bid_ask_response = self.client.get_best_bid_ask(product_ids=spot_product_ids)
                if best_bid_ask_response and best_bid_ask_response.pricebooks:
                    for pb in best_bid_ask_response.pricebooks:
                        if pb.bids and pb.asks and pb.bids[0].price and pb.asks[0].price:
                            bid = self._str_to_float(pb.bids[0].price)
                            ask = self._str_to_float(pb.asks[0].price)
                            if bid > 0 and ask > 0:
                                mid_price = (bid + ask) / 2
                                std_sym = self.convert_to_spot_standardized_symbol(pb.product_id)
                                if std_sym:
                                    mark_prices[std_sym] = mid_price
            except HTTPError as e:
                logger.error(f"Coinbase API error getting spot best bid/ask: {e}", exc_info=True)
            except Exception as e:
                logger.error(f"Unexpected error getting Coinbase spot best bid/ask: {e}", exc_info=True)

        # Get Futures Mark Prices (using get_product as get_futures_market_summary is not available)
        if future_product_ids:
            try:
                for product_id in future_product_ids:
                    product_response = self.client.get_product(product_id=product_id)
                    if product_response and product_response.mid_market_price:
                        mark_p = self._str_to_float(product_response.mid_market_price)
                        std_sym = self.convert_to_perp_standardized_symbol(product_id)
                        if std_sym:
                            mark_prices[std_sym] = mark_p
            except HTTPError as e:
                logger.error(f"Coinbase API error getting futures product info for mark price: {e}", exc_info=True)
            except Exception as e:
                logger.error(f"Unexpected error getting Coinbase futures product info for mark price: {e}", exc_info=True)
                
        return mark_prices

    async def _get_current_price_async(self, exchange_symbol: str, session: aiohttp.ClientSession) -> float:
        """ Helper for async price fetching. exchange_symbol is product_id """
        return await self._get_price_with_subscription("coinbase", exchange_symbol, session)

    def _get_current_price(self, exchange_symbol: str) -> float:
        """Get current price for an exchange-specific symbol (product_id)."""
        async def fetch():
            async with aiohttp.ClientSession() as session:
                return await self._get_current_price_async(exchange_symbol, session)
        try:
            return asyncio.run(fetch())
        except RuntimeError: # If already in an event loop
             loop = asyncio.get_event_loop()
             return loop.run_until_complete(fetch())


    def get_tickers(self, category: str) -> Dict[str, Dict[str, Any]]:
        """Get tickers for a specific category (spot or futures)."""
        tickers: Dict[str, Dict[str, Any]] = {}
        
        product_type_map = {
            'spot': 'SPOT',
            'futures': 'FUTURE',
            'linear': 'FUTURE'
        }
        target_product_type = product_type_map.get(category.lower())

        if not target_product_type:
            logger.warning(f"Unsupported category for Coinbase get_tickers: {category}")
            return {}

        try:
            products_response = self.client.get_products()
            
            if not products_response or not products_response.products:
                logger.error("No products received from Coinbase API for get_tickers.")
                return {}

            for prod in products_response.products:
                if prod.product_type == target_product_type:
                    exchange_symbol = prod.product_id
                    
                    if target_product_type == 'SPOT':
                        tickers[exchange_symbol] = {
                            'symbol': exchange_symbol,
                            'lastPrice': self._str_to_float(prod.price),
                            'volume': self._str_to_float(prod.volume_24h),
                            'priceChangePercent': self._str_to_float(prod.price_percentage_change_24h),
                        }
                    elif target_product_type == 'FUTURE':
                        if prod.futures_product_details:
                            fpd = prod.futures_product_details
                            tickers[exchange_symbol] = {
                                'symbol': exchange_symbol,
                                'markPrice': self._str_to_float(prod.mid_market_price),
                                'lastPrice': self._str_to_float(prod.price),
                                'openInterest': self._str_to_float(fpd.open_interest),
                                'volume': self._str_to_float(prod.volume_24h),
                                'fundingRate': self._str_to_float(fpd.current_funding_rate),
                                'nextFundingTime': fpd.next_funding_time.isoformat() if fpd.next_funding_time else None,
                            }
        except HTTPError as e:
            logger.error(f"Coinbase API error getting tickers for {category}: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Unexpected error getting Coinbase tickers for {category}: {e}", exc_info=True)

        return tickers


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    print("Coinbase Exchange Integration Test Wizard")
    print("=" * 40)

    # --- Part 1: Portfolio UUID Selection ---
    print("\n--- Part 1: Portfolio UUID Selection ---")
    json_file_path = 'coinbasekeys.json'
    try:
        with open(json_file_path, 'r') as f:
            credentials = json.load(f)
        cb_api_key = credentials.get("name")
        cb_api_secret = credentials.get("privateKey")
        # Ensure the private key has correct newline characters if stored with escaped newlines
        if cb_api_secret and '\\n' in cb_api_secret:
             cb_api_secret = cb_api_secret.replace('\\n', '\n')
    except (FileNotFoundError, json.JSONDecodeError, AttributeError) as e:
        print(f"Error reading Coinbase API credentials from {json_file_path}: {e}")
        print(f"Please ensure '{json_file_path}' exists in the workspace root and contains valid JSON with 'name' and 'privateKey' keys.")
        sys.exit(1)

    if not cb_api_key or not cb_api_secret:
        print(f"Error: 'name' or 'privateKey' key not found in {json_file_path}")
        print("Please ensure the JSON file has 'name' and 'privateKey' keys with your credentials.")
        sys.exit(1)
    
    try:
        # Initialize a temporary client for initial checks
        temp_client = RESTClient(api_key=cb_api_key, api_secret=cb_api_secret)

        print("\n--- Verifying API Key Permissions ---")
        permissions_response = None
        try:
            permissions_response = temp_client.get_api_key_permissions()
            print("API Key Permissions:")
            # Check for specific permission attributes instead of iterating a 'permissions' list
            if permissions_response:
                print(f"  Can View: {permissions_response.can_view}")
                print(f"  Can Trade: {permissions_response.can_trade}")
                print(f"  Can Transfer: {permissions_response.can_transfer}")
                # You can add checks for portfolio_uuid and portfolio_type if needed
                # print(f"  Portfolio UUID: {permissions_response.portfolio_uuid}")
                # print(f"  Portfolio Type: {permissions_response.portfolio_type}")
            else:
                print("  Could not retrieve permissions.")
            print("-" * 40)
        except HTTPError as e:
             print(f"Error fetching API key permissions: {e}")
             print("Please ensure your API key is valid and has the 'data:read' permission.")
             sys.exit(1)
        except Exception as e:
             print(f"Unexpected error fetching API key permissions: {e}")
             sys.exit(1)
    except HTTPError as e:
        print(f"Coinbase API Error during temporary client initialization or permission check: {e}")
        print("Please ensure your API key and secret are correct.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error during temporary Coinbase client initialization or permission check: {e}")
        sys.exit(1)

    # Check if portfolio_uuid is available in permissions response (for restricted keys)
    determined_portfolio_uuid = None
    if permissions_response and hasattr(permissions_response, 'portfolio_uuid') and permissions_response.portfolio_uuid:
        determined_portfolio_uuid = permissions_response.portfolio_uuid
        print(f"\nAPI key is restricted to portfolio: {determined_portfolio_uuid}")
        print("Skipping portfolio listing and using this UUID.")
    else:
        # If key is not restricted, try to list portfolios (requires portfolio:read permission)
        print("\nAPI key is not restricted to a single portfolio or portfolio_uuid not available in permissions.")
        print("Attempting to list all portfolios (requires 'portfolio:read' permission)...")
        try:
            portfolios_response = temp_client.get_portfolios()

            print("Available Portfolios:")
            if portfolios_response and portfolios_response.portfolios:
                for p in portfolios_response.portfolios:
                    print(f"  Name: {p.name}, UUID: {p.uuid}, Type: {p.type}")
                else:
                    print("  No portfolios found or error fetching portfolios.")
                    print("Please ensure your API key has the 'portfolio:read' permission if you need to list portfolios.")
                    sys.exit(1)

            print("\nINSTRUCTIONS: Copy the UUID of the portfolio you want to use for testing.")
            print("Then, paste it into the `DEFAULT_PORTFOLIO_UUID` variable directly in this script's __main__ block.")
            DEFAULT_PORTFOLIO_UUID = "YOUR_PORTFOLIO_UUID_HERE" # <--- PASTE YOUR UUID HERE

            if DEFAULT_PORTFOLIO_UUID == "YOUR_PORTFOLIO_UUID_HERE":
                print("\nPlease update DEFAULT_PORTFOLIO_UUID in the script and re-run.")
                sys.exit(0)

            determined_portfolio_uuid = DEFAULT_PORTFOLIO_UUID # Use the manually provided UUID

        except HTTPError as e:
            print(f"\nError fetching portfolios: {e}")
            print("Please ensure your API key has the 'portfolio:read' permission.")
            sys.exit(1)
        except Exception as e:
            print(f"\nUnexpected error fetching portfolios: {e}")
            sys.exit(1)

    if not determined_portfolio_uuid:
        print("\nCould not determine a portfolio UUID to use.")
        sys.exit(1)

    print(f"\nUsing Portfolio UUID: {determined_portfolio_uuid}")
    print("Initializing CoinbaseExchange...")
    try:
        # Initialize the main CoinbaseExchange client with the determined UUID
        exchange = CoinbaseExchange(portfolio_uuid=determined_portfolio_uuid)
        print("CoinbaseExchange initialized successfully.")
    except Exception as e:
        print(f"Error initializing CoinbaseExchange: {e}")
        sys.exit(1)

    # --- Part 2: Test get_standardized_instrument_info ---
    print("\n--- Part 2: Testing get_standardized_instrument_info ---")
    try:
        instrument_info = exchange.instrument_standardized_info # Already fetched in init
        if instrument_info:
            print(f"Fetched {len(instrument_info)} instruments.")
            print("Sample (first 3 Spot, first 3 Futures if available):")
            spot_samples = [inst for inst in instrument_info if inst['is_spot']][:3]
            future_samples = [inst for inst in instrument_info if inst['is_perpetual'] or inst['is_dated_future']][:3]

            if spot_samples:
                print("Spot Samples:")
                for s in spot_samples:
                    print(f"  {s['exchange_format_symbol']} (Base: {s['base_coin']}, Quote: {s['quote_coin']})")
            if future_samples:
                print("Futures Samples:")
                for f in future_samples:
                     print(f"  {f['exchange_format_symbol']} (Base: {f['base_coin']}, Quote: {f['quote_coin']}, Perpetual: {f['is_perpetual']})")
        else:
            print("No instrument info fetched.")
    except Exception as e:
        print(f"Error testing get_standardized_instrument_info: {e}")

    # --- Part 3: Test get_standardized_unified_data & Field Verification ---
    print("\n--- Part 3: Testing get_standardized_unified_data & Field Verification ---")
    try:
        summary_df, assets_df = exchange.get_standardized_unified_data()
        print("Summary DataFrame:")
        print(summary_df.to_string())
        print("\nAssets DataFrame (first 5 rows):")
        print(assets_df.head().to_string())

        print("\n--- Verification Data from Portfolio Breakdown ---")
        # Add print statements for breakdown object
        portfolio_bdown = exchange.client.get_portfolio_breakdown(portfolio_uuid=exchange.portfolio_uuid)
        print(f"Type of portfolio_bdown: {type(portfolio_bdown)}")
        print(f"Dir of portfolio_bdown: {dir(portfolio_bdown)}")
        if portfolio_bdown:
            print("Candidates for 'total_unrealized_pnl':")
            if hasattr(portfolio_bdown, 'unrealized_pnl') and portfolio_bdown.unrealized_pnl and hasattr(portfolio_bdown.unrealized_pnl, 'current'):
                 print(f"  portfolio_breakdown.unrealized_pnl.current.all_value: {portfolio_bdown.unrealized_pnl.current.all_value}")
            if hasattr(portfolio_bdown, 'futures_unrealized_pnl') and portfolio_bdown.futures_unrealized_pnl and hasattr(portfolio_bdown.futures_unrealized_pnl.current):
                 print(f"  portfolio_breakdown.futures_unrealized_pnl.current.all_value: {portfolio_bdown.futures_unrealized_pnl.current.all_value}")

            print("\nCandidates for 'total_available_balance':")
            if hasattr(portfolio_bdown, 'total_available_balance_in_usd'):
                print(f"  portfolio_breakdown.total_available_balance_in_usd.value: {portfolio_bdown.total_available_balance_in_usd.value}")
            if hasattr(portfolio_bdown, 'buying_power'):
                 print(f"  portfolio_breakdown.buying_power.value: {portfolio_bdown.buying_power.value}")

            print("\nINSTRUCTIONS: Compare the 'total_unrealized_pnl' and 'total_available_balance' in the Summary DataFrame")
            print("with the candidate values printed above from your live account data.")
            print("Update the get_standardized_unified_data method if the default choices are incorrect.")
        else:
            print("Could not fetch portfolio breakdown for verification.")

    except Exception as e:
        print(f"Error testing get_standardized_unified_data: {e}")

    # --- Part 4: Test get_eligible_collateral_qty ---
    print("\n--- Part 4: Testing get_eligible_collateral_qty ---")
    test_collateral_symbols = ["BTC-USD", "ETH-USD", "USDC-USD", "SOL-USD"] # Adjust if needed
    for std_sym_collateral in test_collateral_symbols:
        try:
            # Ensure instrument info is loaded to get base_coin for haircut rates
            if not exchange.instrument_standardized_info:
                exchange.initialize_standardized_instrument_info()

            usd_val = 10000.0 # Test with $10,000 USD value
            eligible_qty_usd = exchange.get_eligible_collateral_qty(std_sym_collateral, usd_val)
            base_c = std_sym_collateral.split('-')[0]
            haircut = exchange._collateral_haircut_cache.get(base_c, "N/A")
            print(f"  Eligible collateral for {std_sym_collateral} (value ${usd_val:,.2f}): ${eligible_qty_usd:,.2f} (Haircut: {haircut})")
        except Exception as e:
            print(f"  Error testing get_eligible_collateral_qty for {std_sym_collateral}: {e}")

    # --- Part 5: Test get_balances ---
    print("\n--- Part 5: Testing get_balances ---")
    try:
        balances = exchange.get_balances(nonzero_only=False)
        if balances:
            print(f"Fetched {len(balances)} balances. Sample (first 5):")
            for b in balances[:5]:
                # Add print statements for account object
                print(f"Type of account object: {type(b)}")
                print(f"Dir of account object: {dir(b)}")
                print(f"  Symbol: {b.symbol}, Total: {b.total}, Available: {b.available}, In Order: {b.in_order}")
        else:
            print("No balances found or an error occurred.")
    except Exception as e:
        print(f"  Error testing get_balances: {e}")

    # --- Part 6: Test get_positions ---
    print("\n--- Part 6: Testing get_positions ---")
    try:
        positions = exchange.get_positions(nonzero_only=False)
        if positions:
            print(f"Fetched {len(positions)} positions. Sample (first 5):")
            for p in positions[:5]:
                print(f"  Symbol: {p.symbol}, Size: {p.size}, Entry: {p.entry_price}, Mark: {p.mark_price}, PnL: {p.unrealized_pnl}")
        else:
            print("No positions found (this is normal if you have no open futures positions).")
    except Exception as e:
        print(f"  Error testing get_positions: {e}")

    # --- Part 7: Test get_mark_prices ---
    print("\n--- Part 7: Testing get_mark_prices ---")
    # Pick some symbols from instrument_info if available
    test_mark_price_symbols = []
    if exchange.instrument_standardized_info:
        spot_syms = [inst['standardized_format_symbol'] for inst in exchange.instrument_standardized_info if inst['is_spot'] and inst['base_coin'] in ['BTC', 'ETH'] and inst['quote_coin'] in ['USD', 'USDT']]
        perp_syms = [inst['standardized_format_perp'] for inst in exchange.instrument_standardized_info if inst['is_perpetual'] and inst['base_coin'] in ['BTC', 'ETH'] and inst['quote_coin'] in ['USD', 'USDT']]
        test_mark_price_symbols.extend(spot_syms[:2])
        test_mark_price_symbols.extend(perp_syms[:2])
        test_mark_price_symbols = list(set(test_mark_price_symbols)) # Unique

    if not test_mark_price_symbols:
        test_mark_price_symbols = ["BTC-USD", "ETH-USD.PERP"] # Fallback if no dynamic symbols found

    if test_mark_price_symbols:
        print(f"Testing for symbols: {test_mark_price_symbols}")
        try:
            mark_prices = exchange.get_mark_prices(test_mark_price_symbols)
            print("Mark Prices:")
            for sym, price in mark_prices.items():
                print(f"  {sym}: {price}")
        except Exception as e:
            print(f"  Error testing get_mark_prices: {e}")
    else:
        print("No symbols to test for get_mark_prices.")

    # --- Part 8: Test get_tickers ---
    print("\n--- Part 8: Testing get_tickers ---")
    try:
        print("Spot Tickers (sample):")
        spot_tickers = exchange.get_tickers(category='spot')
        if spot_tickers:
            count = 0
            for sym, data in spot_tickers.items():
                if any(x in sym for x in ['BTC', 'ETH']): # Print only a few relevant ones
                    print(f"  {sym}: LastPrice={data.get('lastPrice')}, Volume={data.get('volume')}")
                    count+=1
                if count >=3: break
            if not count: print("  No BTC/ETH spot tickers found in sample.")
        else:
            print("  No spot tickers fetched.")

        print("\nFutures Tickers (sample):")
        futures_tickers = exchange.get_tickers(category='futures')
        if futures_tickers:
            count = 0
            for sym, data in futures_tickers.items():
                if any(x in sym for x in ['BTC', 'ETH']): # Print only a few relevant ones
                     print(f"  {sym}: MarkPrice={data.get('markPrice')}, OpenInterest={data.get('openInterest')}")
                     count+=1
                if count >= 3: break
            if not count: print("  No BTC/ETH futures tickers found in sample.")
        else:
            print("  No futures tickers fetched.")
    except Exception as e:
        print(f"  Error testing get_tickers: {e}")

    print("\n=" * 40)
    print("Coinbase Exchange Test Wizard Finished.")
    print("Remember to verify PnL and Available Balance fields based on the output.")
